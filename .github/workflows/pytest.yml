name: Tests

on:
  # Allow to manually trigger through github API
  workflow_dispatch:

  # Triggers with push to main
  push:
    branches:
      - solutions

  # Triggers with push to a PR aimed at main
  pull_request:
    branches:
      - solutions

env:

  package-name: rl_exercises
  test-dir: tests
  UV_SYSTEM_PYTHON: 1

  # Arguments used for pytest
  pytest-args: >-
    --durations=20
    -v

jobs:
  tests:
    name: testing
    runs-on: ubuntu-latest
    defaults:
      run:
        shell: bash -l {0}  # Default to using bash on all and load (-l) .bashrc which miniconda uses

    steps:
          - name: Checkout
            uses: actions/checkout@v4
    
          - name: Install uv
            uses: astral-sh/setup-uv@v5
            with:
              # Install a specific version of uv.
              version: "0.6.14"
    
          - name: "Set up Python"
            uses: actions/setup-python@v5
            with:
              python-version-file: "pyproject.toml"
    
          - name: Install ${{ env.package-name }}
            run: make install
    
          - name: Run tests
            run: make test
