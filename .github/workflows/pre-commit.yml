name: pre-commit

on:
  # Manually triggerable in github
  workflow_dispatch:

  # When a push occurs on either of these branches
  push:
    branches:
      - main
      - development

  # When a push occurs on a PR that targets these branches
  pull_request:
    branches:
      - main
      - development

env:
  package-name: "rl_exercises"
  UV_SYSTEM_PYTHON: 1

jobs:

  run-all-files:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          # Install a specific version of uv.
          version: "0.6.14"

      - name: "Set up Python"
        uses: actions/setup-python@v5
        with:
          python-version-file: "pyproject.toml"

      - name: Install ${{ env.package-name }}
        run: make install

      - name: Run pre-commit
        run: make pre-commit
