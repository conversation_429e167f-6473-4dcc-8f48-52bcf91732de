{"cells": [{"cell_type": "markdown", "id": "a37d9151-dcbf-4c7f-8d0b-c697b3fdbc13", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# RL Exercise Demo\n", "This exercise serves as a demonstration how to quickly train an RL agent on a popular environment with a RL framework.\n", "We will\n", "1. Select and instantiate environment (gym's BipedalWalker-v3).\n", "2. Select and setup our RL algorithm / agent (stablebaselines3 [SAC](https://spinningup.openai.com/en/latest/algorithms/sac.html)).\n", "3. Train the agent on the environment and visualize training progress.\n", "4. Evaluate our agent and observe the distribution of test performances.\n", "5. Record and replay the agent, before and after."]}, {"cell_type": "markdown", "id": "d3c4b5e2-e487-4b04-8c55-3938b4d82529", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## The Environment: B<PERSON><PERSON><PERSON> Walker\n", "![<PERSON><PERSON><PERSON><PERSON>](bipedal_walker.gif) [credits](https://www.gymlibrary.dev/_images/bipedal_walker.gif)\n", "\n", "![Interaction Env-Agent](env_agent.png)\n", "\n", "### Env\n", "- locomotion\n", "- 4 joints\n", "\n", "### Action Space\n", "- motor speed for all 4 joints (hips and knees) [-1, 1]\n", "\n", "### Observation Space\n", "State consists of\n", "- hull angle speed\n", "- angular velocity\n", "- horizontal speed\n", "- vertical speed\n", "- position of joints\n", "- joints angular speed\n", "- legs contact with ground\n", "- 10 lidar rangefinder measurements\n", "\n", "There are no coordinates! State vector with 24 entries.\n", "\n", "### Rewards\n", "Moving forward gives rewards.\n", "Falling is punished.\n", "Applying motor torque costs a little.\n", "\n", "\n", "### Starting State\n", "Stands at the left in a certain position.\n", "\n", "### Episode Termination\n", "- walker falls\n", "- or reaches end of terrain\n"]}, {"cell_type": "code", "execution_count": 1, "id": "22de499d-f2cd-4d95-b61b-1827650d001b", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import pandas as pd\n", "\n", "model_fn = \"trained_agent.zip\"\n", "env_id = \"BipedalWalker-v3\"\n", "log_dir = \"logs/tensorboard\""]}, {"cell_type": "markdown", "id": "a267d6a0-dcd5-42d2-83e2-a70cf4518e36", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["## The Agent: SAC\n", "SAC: Soft Actor Critic\n", "[[paper]](https://arxiv.org/abs/1801.01290) [[blogpost]](https://spinningup.openai.com/en/latest/algorithms/sac.html)\n", "\n", "- off-policy algorithm\n", "- for continuous actions\n", "- one actor, one critic\n", "- special regularization to steer exploration-exploitation trade-off "]}, {"cell_type": "code", "execution_count": null, "id": "6b794a7c-86f1-4675-aa8f-9e81f2ca4f4b", "metadata": {"pycharm": {"name": "#%%\n"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using cpu device\n", "Wrapping the env with a `Monitor` wrapper\n", "Wrapping the env in a DummyVecEnv.\n", "Logging to logs/tensorboard/SAC_1\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 456      |\n", "|    ep_rew_mean     | -104     |\n", "| time/              |          |\n", "|    episodes        | 4        |\n", "|    fps             | 148      |\n", "|    time_elapsed    | 12       |\n", "|    total_timesteps | 1822     |\n", "| train/             |          |\n", "|    actor_loss      | -16.9    |\n", "|    critic_loss     | 40.8     |\n", "|    ent_coef        | 0.597    |\n", "|    ent_coef_loss   | -3.41    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 1721     |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 645      |\n", "|    ep_rew_mean     | -95.6    |\n", "| time/              |          |\n", "|    episodes        | 8        |\n", "|    fps             | 168      |\n", "|    time_elapsed    | 30       |\n", "|    total_timesteps | 5162     |\n", "| train/             |          |\n", "|    actor_loss      | -27.4    |\n", "|    critic_loss     | 0.172    |\n", "|    ent_coef        | 0.222    |\n", "|    ent_coef_loss   | -9.67    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 5061     |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 710      |\n", "|    ep_rew_mean     | -93.3    |\n", "| time/              |          |\n", "|    episodes        | 12       |\n", "|    fps             | 177      |\n", "|    time_elapsed    | 48       |\n", "|    total_timesteps | 8524     |\n", "| train/             |          |\n", "|    actor_loss      | -26.6    |\n", "|    critic_loss     | 0.0997   |\n", "|    ent_coef        | 0.0819   |\n", "|    ent_coef_loss   | -15.4    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 8423     |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 552      |\n", "|    ep_rew_mean     | -96.6    |\n", "| time/              |          |\n", "|    episodes        | 16       |\n", "|    fps             | 176      |\n", "|    time_elapsed    | 49       |\n", "|    total_timesteps | 8825     |\n", "| train/             |          |\n", "|    actor_loss      | -26.5    |\n", "|    critic_loss     | 36.8     |\n", "|    ent_coef        | 0.0751   |\n", "|    ent_coef_loss   | -15.2    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 8724     |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 465      |\n", "|    ep_rew_mean     | -99.3    |\n", "| time/              |          |\n", "|    episodes        | 20       |\n", "|    fps             | 177      |\n", "|    time_elapsed    | 52       |\n", "|    total_timesteps | 9305     |\n", "| train/             |          |\n", "|    actor_loss      | -25.4    |\n", "|    critic_loss     | 2.08     |\n", "|    ent_coef        | 0.0658   |\n", "|    ent_coef_loss   | -14.7    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 9204     |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 402      |\n", "|    ep_rew_mean     | -100     |\n", "| time/              |          |\n", "|    episodes        | 24       |\n", "|    fps             | 178      |\n", "|    time_elapsed    | 54       |\n", "|    total_timesteps | 9639     |\n", "| train/             |          |\n", "|    actor_loss      | -25.6    |\n", "|    critic_loss     | 0.458    |\n", "|    ent_coef        | 0.0602   |\n", "|    ent_coef_loss   | -13.6    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 9538     |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 352      |\n", "|    ep_rew_mean     | -101     |\n", "| time/              |          |\n", "|    episodes        | 28       |\n", "|    fps             | 177      |\n", "|    time_elapsed    | 55       |\n", "|    total_timesteps | 9868     |\n", "| train/             |          |\n", "|    actor_loss      | -24.2    |\n", "|    critic_loss     | 1.39     |\n", "|    ent_coef        | 0.0568   |\n", "|    ent_coef_loss   | -13.7    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 9767     |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 319      |\n", "|    ep_rew_mean     | -102     |\n", "| time/              |          |\n", "|    episodes        | 32       |\n", "|    fps             | 177      |\n", "|    time_elapsed    | 57       |\n", "|    total_timesteps | 10214    |\n", "| train/             |          |\n", "|    actor_loss      | -23.3    |\n", "|    critic_loss     | 5.6      |\n", "|    ent_coef        | 0.052    |\n", "|    ent_coef_loss   | -12.7    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 10113    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 291      |\n", "|    ep_rew_mean     | -102     |\n", "| time/              |          |\n", "|    episodes        | 36       |\n", "|    fps             | 178      |\n", "|    time_elapsed    | 58       |\n", "|    total_timesteps | 10460    |\n", "| train/             |          |\n", "|    actor_loss      | -23.2    |\n", "|    critic_loss     | 54.8     |\n", "|    ent_coef        | 0.0489   |\n", "|    ent_coef_loss   | -13.3    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 10359    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 267      |\n", "|    ep_rew_mean     | -102     |\n", "| time/              |          |\n", "|    episodes        | 40       |\n", "|    fps             | 178      |\n", "|    time_elapsed    | 59       |\n", "|    total_timesteps | 10693    |\n", "| train/             |          |\n", "|    actor_loss      | -21.8    |\n", "|    critic_loss     | 3.97     |\n", "|    ent_coef        | 0.0463   |\n", "|    ent_coef_loss   | -12      |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 10592    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 252      |\n", "|    ep_rew_mean     | -104     |\n", "| time/              |          |\n", "|    episodes        | 44       |\n", "|    fps             | 178      |\n", "|    time_elapsed    | 61       |\n", "|    total_timesteps | 11073    |\n", "| train/             |          |\n", "|    actor_loss      | -21.6    |\n", "|    critic_loss     | 2.26     |\n", "|    ent_coef        | 0.0427   |\n", "|    ent_coef_loss   | -9.67    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 10972    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 244      |\n", "|    ep_rew_mean     | -105     |\n", "| time/              |          |\n", "|    episodes        | 48       |\n", "|    fps             | 179      |\n", "|    time_elapsed    | 65       |\n", "|    total_timesteps | 11729    |\n", "| train/             |          |\n", "|    actor_loss      | -20.2    |\n", "|    critic_loss     | 2.61     |\n", "|    ent_coef        | 0.0372   |\n", "|    ent_coef_loss   | -6.59    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 11628    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 232      |\n", "|    ep_rew_mean     | -106     |\n", "| time/              |          |\n", "|    episodes        | 52       |\n", "|    fps             | 179      |\n", "|    time_elapsed    | 67       |\n", "|    total_timesteps | 12066    |\n", "| train/             |          |\n", "|    actor_loss      | -23.1    |\n", "|    critic_loss     | 1.48     |\n", "|    ent_coef        | 0.0347   |\n", "|    ent_coef_loss   | -7.89    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 11965    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 222      |\n", "|    ep_rew_mean     | -107     |\n", "| time/              |          |\n", "|    episodes        | 56       |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 68       |\n", "|    total_timesteps | 12421    |\n", "| train/             |          |\n", "|    actor_loss      | -19.4    |\n", "|    critic_loss     | 2.01     |\n", "|    ent_coef        | 0.0325   |\n", "|    ent_coef_loss   | -5.35    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 12320    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 213      |\n", "|    ep_rew_mean     | -107     |\n", "| time/              |          |\n", "|    episodes        | 60       |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 70       |\n", "|    total_timesteps | 12778    |\n", "| train/             |          |\n", "|    actor_loss      | -20.7    |\n", "|    critic_loss     | 2.25     |\n", "|    ent_coef        | 0.0305   |\n", "|    ent_coef_loss   | -7.12    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 12677    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 204      |\n", "|    ep_rew_mean     | -107     |\n", "| time/              |          |\n", "|    episodes        | 64       |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 72       |\n", "|    total_timesteps | 13030    |\n", "| train/             |          |\n", "|    actor_loss      | -19.5    |\n", "|    critic_loss     | 2.16     |\n", "|    ent_coef        | 0.0292   |\n", "|    ent_coef_loss   | -4.94    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 12929    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 195      |\n", "|    ep_rew_mean     | -108     |\n", "| time/              |          |\n", "|    episodes        | 68       |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 73       |\n", "|    total_timesteps | 13285    |\n", "| train/             |          |\n", "|    actor_loss      | -16.4    |\n", "|    critic_loss     | 5.63     |\n", "|    ent_coef        | 0.0281   |\n", "|    ent_coef_loss   | -4.72    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 13184    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 190      |\n", "|    ep_rew_mean     | -109     |\n", "| time/              |          |\n", "|    episodes        | 72       |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 75       |\n", "|    total_timesteps | 13656    |\n", "| train/             |          |\n", "|    actor_loss      | -17      |\n", "|    critic_loss     | 10.8     |\n", "|    ent_coef        | 0.0269   |\n", "|    ent_coef_loss   | -2.78    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 13555    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 184      |\n", "|    ep_rew_mean     | -109     |\n", "| time/              |          |\n", "|    episodes        | 76       |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 77       |\n", "|    total_timesteps | 14001    |\n", "| train/             |          |\n", "|    actor_loss      | -17.1    |\n", "|    critic_loss     | 2.26     |\n", "|    ent_coef        | 0.0264   |\n", "|    ent_coef_loss   | -2       |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 13900    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 178      |\n", "|    ep_rew_mean     | -109     |\n", "| time/              |          |\n", "|    episodes        | 80       |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 78       |\n", "|    total_timesteps | 14266    |\n", "| train/             |          |\n", "|    actor_loss      | -14      |\n", "|    critic_loss     | 4.62     |\n", "|    ent_coef        | 0.0267   |\n", "|    ent_coef_loss   | 1.41     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 14165    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 174      |\n", "|    ep_rew_mean     | -110     |\n", "| time/              |          |\n", "|    episodes        | 84       |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 80       |\n", "|    total_timesteps | 14583    |\n", "| train/             |          |\n", "|    actor_loss      | -11.6    |\n", "|    critic_loss     | 6.3      |\n", "|    ent_coef        | 0.0279   |\n", "|    ent_coef_loss   | 1.42     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 14482    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 170      |\n", "|    ep_rew_mean     | -110     |\n", "| time/              |          |\n", "|    episodes        | 88       |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 82       |\n", "|    total_timesteps | 14943    |\n", "| train/             |          |\n", "|    actor_loss      | -14.2    |\n", "|    critic_loss     | 4.69     |\n", "|    ent_coef        | 0.0295   |\n", "|    ent_coef_loss   | 1.52     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 14842    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 186      |\n", "|    ep_rew_mean     | -110     |\n", "| time/              |          |\n", "|    episodes        | 92       |\n", "|    fps             | 181      |\n", "|    time_elapsed    | 93       |\n", "|    total_timesteps | 17066    |\n", "| train/             |          |\n", "|    actor_loss      | -7.54    |\n", "|    critic_loss     | 3.51     |\n", "|    ent_coef        | 0.0324   |\n", "|    ent_coef_loss   | 0.17     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 16965    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 181      |\n", "|    ep_rew_mean     | -110     |\n", "| time/              |          |\n", "|    episodes        | 96       |\n", "|    fps             | 182      |\n", "|    time_elapsed    | 95       |\n", "|    total_timesteps | 17412    |\n", "| train/             |          |\n", "|    actor_loss      | -12.1    |\n", "|    critic_loss     | 1.86     |\n", "|    ent_coef        | 0.0322   |\n", "|    ent_coef_loss   | -1.51    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 17311    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 207      |\n", "|    ep_rew_mean     | -110     |\n", "| time/              |          |\n", "|    episodes        | 100      |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 114      |\n", "|    total_timesteps | 20732    |\n", "| train/             |          |\n", "|    actor_loss      | -5.58    |\n", "|    critic_loss     | 3.1      |\n", "|    ent_coef        | 0.0241   |\n", "|    ent_coef_loss   | 1.25     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 20631    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 208      |\n", "|    ep_rew_mean     | -110     |\n", "| time/              |          |\n", "|    episodes        | 104      |\n", "|    fps             | 180      |\n", "|    time_elapsed    | 125      |\n", "|    total_timesteps | 22629    |\n", "| train/             |          |\n", "|    actor_loss      | -6.99    |\n", "|    critic_loss     | 2.9      |\n", "|    ent_coef        | 0.0219   |\n", "|    ent_coef_loss   | -0.315   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 22528    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 224      |\n", "|    ep_rew_mean     | -110     |\n", "| time/              |          |\n", "|    episodes        | 108      |\n", "|    fps             | 183      |\n", "|    time_elapsed    | 150      |\n", "|    total_timesteps | 27611    |\n", "| train/             |          |\n", "|    actor_loss      | -6.73    |\n", "|    critic_loss     | 2.14     |\n", "|    ent_coef        | 0.0191   |\n", "|    ent_coef_loss   | -1.71    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 27510    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 226      |\n", "|    ep_rew_mean     | -111     |\n", "| time/              |          |\n", "|    episodes        | 112      |\n", "|    fps             | 183      |\n", "|    time_elapsed    | 169      |\n", "|    total_timesteps | 31144    |\n", "| train/             |          |\n", "|    actor_loss      | -6.4     |\n", "|    critic_loss     | 4.22     |\n", "|    ent_coef        | 0.0161   |\n", "|    ent_coef_loss   | 0.229    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 31043    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 242      |\n", "|    ep_rew_mean     | -111     |\n", "| time/              |          |\n", "|    episodes        | 116      |\n", "|    fps             | 183      |\n", "|    time_elapsed    | 179      |\n", "|    total_timesteps | 33053    |\n", "| train/             |          |\n", "|    actor_loss      | -7.63    |\n", "|    critic_loss     | 2.56     |\n", "|    ent_coef        | 0.0152   |\n", "|    ent_coef_loss   | 0.524    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 32952    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 243      |\n", "|    ep_rew_mean     | -110     |\n", "| time/              |          |\n", "|    episodes        | 120      |\n", "|    fps             | 183      |\n", "|    time_elapsed    | 183      |\n", "|    total_timesteps | 33635    |\n", "| train/             |          |\n", "|    actor_loss      | -6.98    |\n", "|    critic_loss     | 2.97     |\n", "|    ent_coef        | 0.0156   |\n", "|    ent_coef_loss   | -2.95    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 33534    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 249      |\n", "|    ep_rew_mean     | -111     |\n", "| time/              |          |\n", "|    episodes        | 124      |\n", "|    fps             | 183      |\n", "|    time_elapsed    | 188      |\n", "|    total_timesteps | 34576    |\n", "| train/             |          |\n", "|    actor_loss      | -5.69    |\n", "|    critic_loss     | 3.6      |\n", "|    ent_coef        | 0.0159   |\n", "|    ent_coef_loss   | 0.558    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 34475    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 254      |\n", "|    ep_rew_mean     | -111     |\n", "| time/              |          |\n", "|    episodes        | 128      |\n", "|    fps             | 183      |\n", "|    time_elapsed    | 192      |\n", "|    total_timesteps | 35278    |\n", "| train/             |          |\n", "|    actor_loss      | -2.84    |\n", "|    critic_loss     | 5.43     |\n", "|    ent_coef        | 0.0159   |\n", "|    ent_coef_loss   | 2.76     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 35177    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 256      |\n", "|    ep_rew_mean     | -111     |\n", "| time/              |          |\n", "|    episodes        | 132      |\n", "|    fps             | 183      |\n", "|    time_elapsed    | 195      |\n", "|    total_timesteps | 35847    |\n", "| train/             |          |\n", "|    actor_loss      | -1.13    |\n", "|    critic_loss     | 2.28     |\n", "|    ent_coef        | 0.0162   |\n", "|    ent_coef_loss   | -0.613   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 35746    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 260      |\n", "|    ep_rew_mean     | -111     |\n", "| time/              |          |\n", "|    episodes        | 136      |\n", "|    fps             | 183      |\n", "|    time_elapsed    | 198      |\n", "|    total_timesteps | 36457    |\n", "| train/             |          |\n", "|    actor_loss      | -1.93    |\n", "|    critic_loss     | 1.99     |\n", "|    ent_coef        | 0.0169   |\n", "|    ent_coef_loss   | -2.16    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 36356    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 268      |\n", "|    ep_rew_mean     | -111     |\n", "| time/              |          |\n", "|    episodes        | 140      |\n", "|    fps             | 183      |\n", "|    time_elapsed    | 204      |\n", "|    total_timesteps | 37456    |\n", "| train/             |          |\n", "|    actor_loss      | -1.9     |\n", "|    critic_loss     | 2.85     |\n", "|    ent_coef        | 0.0187   |\n", "|    ent_coef_loss   | 1.45     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 37355    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 293      |\n", "|    ep_rew_mean     | -110     |\n", "| time/              |          |\n", "|    episodes        | 144      |\n", "|    fps             | 183      |\n", "|    time_elapsed    | 219      |\n", "|    total_timesteps | 40340    |\n", "| train/             |          |\n", "|    actor_loss      | -2.78    |\n", "|    critic_loss     | 2.32     |\n", "|    ent_coef        | 0.0218   |\n", "|    ent_coef_loss   | -1.01    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 40239    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 335      |\n", "|    ep_rew_mean     | -112     |\n", "| time/              |          |\n", "|    episodes        | 148      |\n", "|    fps             | 184      |\n", "|    time_elapsed    | 244      |\n", "|    total_timesteps | 45211    |\n", "| train/             |          |\n", "|    actor_loss      | -2.93    |\n", "|    critic_loss     | 2.73     |\n", "|    ent_coef        | 0.0206   |\n", "|    ent_coef_loss   | -0.554   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 45110    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 369      |\n", "|    ep_rew_mean     | -113     |\n", "| time/              |          |\n", "|    episodes        | 152      |\n", "|    fps             | 185      |\n", "|    time_elapsed    | 264      |\n", "|    total_timesteps | 48990    |\n", "| train/             |          |\n", "|    actor_loss      | -2.98    |\n", "|    critic_loss     | 2.49     |\n", "|    ent_coef        | 0.0181   |\n", "|    ent_coef_loss   | 0.247    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 48889    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 367      |\n", "|    ep_rew_mean     | -113     |\n", "| time/              |          |\n", "|    episodes        | 156      |\n", "|    fps             | 185      |\n", "|    time_elapsed    | 265      |\n", "|    total_timesteps | 49137    |\n", "| train/             |          |\n", "|    actor_loss      | -3.36    |\n", "|    critic_loss     | 2.22     |\n", "|    ent_coef        | 0.018    |\n", "|    ent_coef_loss   | -1.19    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 49036    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 366      |\n", "|    ep_rew_mean     | -113     |\n", "| time/              |          |\n", "|    episodes        | 160      |\n", "|    fps             | 185      |\n", "|    time_elapsed    | 266      |\n", "|    total_timesteps | 49365    |\n", "| train/             |          |\n", "|    actor_loss      | -3.69    |\n", "|    critic_loss     | 3.04     |\n", "|    ent_coef        | 0.0179   |\n", "|    ent_coef_loss   | 1.32     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 49264    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 396      |\n", "|    ep_rew_mean     | -112     |\n", "| time/              |          |\n", "|    episodes        | 164      |\n", "|    fps             | 185      |\n", "|    time_elapsed    | 284      |\n", "|    total_timesteps | 52670    |\n", "| train/             |          |\n", "|    actor_loss      | -2.66    |\n", "|    critic_loss     | 1.61     |\n", "|    ent_coef        | 0.0214   |\n", "|    ent_coef_loss   | -1.1     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 52569    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 446      |\n", "|    ep_rew_mean     | -111     |\n", "| time/              |          |\n", "|    episodes        | 168      |\n", "|    fps             | 176      |\n", "|    time_elapsed    | 328      |\n", "|    total_timesteps | 57907    |\n", "| train/             |          |\n", "|    actor_loss      | -1.44    |\n", "|    critic_loss     | 1.25     |\n", "|    ent_coef        | 0.0185   |\n", "|    ent_coef_loss   | -0.192   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 57806    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 507      |\n", "|    ep_rew_mean     | -110     |\n", "| time/              |          |\n", "|    episodes        | 172      |\n", "|    fps             | 170      |\n", "|    time_elapsed    | 378      |\n", "|    total_timesteps | 64307    |\n", "| train/             |          |\n", "|    actor_loss      | 0.145    |\n", "|    critic_loss     | 1.25     |\n", "|    ent_coef        | 0.0142   |\n", "|    ent_coef_loss   | 0.339    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 64206    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 567      |\n", "|    ep_rew_mean     | -109     |\n", "| time/              |          |\n", "|    episodes        | 176      |\n", "|    fps             | 172      |\n", "|    time_elapsed    | 410      |\n", "|    total_timesteps | 70707    |\n", "| train/             |          |\n", "|    actor_loss      | 2.79     |\n", "|    critic_loss     | 0.79     |\n", "|    ent_coef        | 0.0115   |\n", "|    ent_coef_loss   | 0.938    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 70606    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 628      |\n", "|    ep_rew_mean     | -107     |\n", "| time/              |          |\n", "|    episodes        | 180      |\n", "|    fps             | 173      |\n", "|    time_elapsed    | 444      |\n", "|    total_timesteps | 77107    |\n", "| train/             |          |\n", "|    actor_loss      | 2.99     |\n", "|    critic_loss     | 1.17     |\n", "|    ent_coef        | 0.0122   |\n", "|    ent_coef_loss   | 1.24     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 77006    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 689      |\n", "|    ep_rew_mean     | -105     |\n", "| time/              |          |\n", "|    episodes        | 184      |\n", "|    fps             | 172      |\n", "|    time_elapsed    | 483      |\n", "|    total_timesteps | 83507    |\n", "| train/             |          |\n", "|    actor_loss      | 2.33     |\n", "|    critic_loss     | 0.735    |\n", "|    ent_coef        | 0.0132   |\n", "|    ent_coef_loss   | -0.125   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 83406    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 750      |\n", "|    ep_rew_mean     | -103     |\n", "| time/              |          |\n", "|    episodes        | 188      |\n", "|    fps             | 173      |\n", "|    time_elapsed    | 516      |\n", "|    total_timesteps | 89907    |\n", "| train/             |          |\n", "|    actor_loss      | 3.11     |\n", "|    critic_loss     | 1.11     |\n", "|    ent_coef        | 0.0125   |\n", "|    ent_coef_loss   | -1.61    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 89806    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 780      |\n", "|    ep_rew_mean     | -101     |\n", "| time/              |          |\n", "|    episodes        | 192      |\n", "|    fps             | 174      |\n", "|    time_elapsed    | 544      |\n", "|    total_timesteps | 95055    |\n", "| train/             |          |\n", "|    actor_loss      | 4.46     |\n", "|    critic_loss     | 0.564    |\n", "|    ent_coef        | 0.0128   |\n", "|    ent_coef_loss   | -2.36    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 94954    |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 827      |\n", "|    ep_rew_mean     | -98.7    |\n", "| time/              |          |\n", "|    episodes        | 196      |\n", "|    fps             | 170      |\n", "|    time_elapsed    | 586      |\n", "|    total_timesteps | 100128   |\n", "| train/             |          |\n", "|    actor_loss      | 1.82     |\n", "|    critic_loss     | 0.785    |\n", "|    ent_coef        | 0.0113   |\n", "|    ent_coef_loss   | -1.7     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 100027   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 815      |\n", "|    ep_rew_mean     | -97.7    |\n", "| time/              |          |\n", "|    episodes        | 200      |\n", "|    fps             | 170      |\n", "|    time_elapsed    | 600      |\n", "|    total_timesteps | 102232   |\n", "| train/             |          |\n", "|    actor_loss      | 0.761    |\n", "|    critic_loss     | 0.412    |\n", "|    ent_coef        | 0.0106   |\n", "|    ent_coef_loss   | -2.75    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 102131   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 860      |\n", "|    ep_rew_mean     | -95.8    |\n", "| time/              |          |\n", "|    episodes        | 204      |\n", "|    fps             | 170      |\n", "|    time_elapsed    | 638      |\n", "|    total_timesteps | 108632   |\n", "| train/             |          |\n", "|    actor_loss      | 4.19     |\n", "|    critic_loss     | 0.66     |\n", "|    ent_coef        | 0.00882  |\n", "|    ent_coef_loss   | -0.423   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 108531   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 874      |\n", "|    ep_rew_mean     | -95.7    |\n", "| time/              |          |\n", "|    episodes        | 208      |\n", "|    fps             | 167      |\n", "|    time_elapsed    | 688      |\n", "|    total_timesteps | 115032   |\n", "| train/             |          |\n", "|    actor_loss      | 3.03     |\n", "|    critic_loss     | 0.59     |\n", "|    ent_coef        | 0.00684  |\n", "|    ent_coef_loss   | 0.257    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 114931   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 888      |\n", "|    ep_rew_mean     | -94.7    |\n", "| time/              |          |\n", "|    episodes        | 212      |\n", "|    fps             | 166      |\n", "|    time_elapsed    | 718      |\n", "|    total_timesteps | 119908   |\n", "| train/             |          |\n", "|    actor_loss      | 4.5      |\n", "|    critic_loss     | 0.604    |\n", "|    ent_coef        | 0.00717  |\n", "|    ent_coef_loss   | 0.181    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 119807   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 892      |\n", "|    ep_rew_mean     | -94      |\n", "| time/              |          |\n", "|    episodes        | 216      |\n", "|    fps             | 166      |\n", "|    time_elapsed    | 732      |\n", "|    total_timesteps | 122240   |\n", "| train/             |          |\n", "|    actor_loss      | 3.71     |\n", "|    critic_loss     | 0.47     |\n", "|    ent_coef        | 0.00681  |\n", "|    ent_coef_loss   | 0.449    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 122139   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 927      |\n", "|    ep_rew_mean     | -92.8    |\n", "| time/              |          |\n", "|    episodes        | 220      |\n", "|    fps             | 166      |\n", "|    time_elapsed    | 758      |\n", "|    total_timesteps | 126380   |\n", "| train/             |          |\n", "|    actor_loss      | 5.97     |\n", "|    critic_loss     | 0.558    |\n", "|    ent_coef        | 0.00628  |\n", "|    ent_coef_loss   | -0.456   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 126279   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 962      |\n", "|    ep_rew_mean     | -87.7    |\n", "| time/              |          |\n", "|    episodes        | 224      |\n", "|    fps             | 166      |\n", "|    time_elapsed    | 787      |\n", "|    total_timesteps | 130769   |\n", "| train/             |          |\n", "|    actor_loss      | 5.87     |\n", "|    critic_loss     | 0.776    |\n", "|    ent_coef        | 0.00645  |\n", "|    ent_coef_loss   | 1.2      |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 130668   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.01e+03 |\n", "|    ep_rew_mean     | -83.4    |\n", "| time/              |          |\n", "|    episodes        | 228      |\n", "|    fps             | 163      |\n", "|    time_elapsed    | 833      |\n", "|    total_timesteps | 136008   |\n", "| train/             |          |\n", "|    actor_loss      | 5.49     |\n", "|    critic_loss     | 0.39     |\n", "|    ent_coef        | 0.00615  |\n", "|    ent_coef_loss   | -2.41    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 135907   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.05e+03 |\n", "|    ep_rew_mean     | -82.5    |\n", "| time/              |          |\n", "|    episodes        | 232      |\n", "|    fps             | 161      |\n", "|    time_elapsed    | 873      |\n", "|    total_timesteps | 141275   |\n", "| train/             |          |\n", "|    actor_loss      | 4.04     |\n", "|    critic_loss     | 0.532    |\n", "|    ent_coef        | 0.00591  |\n", "|    ent_coef_loss   | -0.346   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 141174   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.11e+03 |\n", "|    ep_rew_mean     | -81.2    |\n", "| time/              |          |\n", "|    episodes        | 236      |\n", "|    fps             | 161      |\n", "|    time_elapsed    | 910      |\n", "|    total_timesteps | 147213   |\n", "| train/             |          |\n", "|    actor_loss      | 5.9      |\n", "|    critic_loss     | 0.804    |\n", "|    ent_coef        | 0.00655  |\n", "|    ent_coef_loss   | -0.376   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 147112   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.16e+03 |\n", "|    ep_rew_mean     | -78.2    |\n", "| time/              |          |\n", "|    episodes        | 240      |\n", "|    fps             | 161      |\n", "|    time_elapsed    | 948      |\n", "|    total_timesteps | 153613   |\n", "| train/             |          |\n", "|    actor_loss      | 4.39     |\n", "|    critic_loss     | 0.219    |\n", "|    ent_coef        | 0.00633  |\n", "|    ent_coef_loss   | -0.895   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 153512   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.19e+03 |\n", "|    ep_rew_mean     | -73.5    |\n", "| time/              |          |\n", "|    episodes        | 244      |\n", "|    fps             | 161      |\n", "|    time_elapsed    | 982      |\n", "|    total_timesteps | 158951   |\n", "| train/             |          |\n", "|    actor_loss      | 4.73     |\n", "|    critic_loss     | 0.3      |\n", "|    ent_coef        | 0.00652  |\n", "|    ent_coef_loss   | 0.998    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 158850   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.2e+03  |\n", "|    ep_rew_mean     | -61.2    |\n", "| time/              |          |\n", "|    episodes        | 248      |\n", "|    fps             | 161      |\n", "|    time_elapsed    | 1021     |\n", "|    total_timesteps | 165351   |\n", "| train/             |          |\n", "|    actor_loss      | 4.53     |\n", "|    critic_loss     | 0.975    |\n", "|    ent_coef        | 0.00642  |\n", "|    ent_coef_loss   | 1.36     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 165250   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.23e+03 |\n", "|    ep_rew_mean     | -48.6    |\n", "| time/              |          |\n", "|    episodes        | 252      |\n", "|    fps             | 161      |\n", "|    time_elapsed    | 1061     |\n", "|    total_timesteps | 171751   |\n", "| train/             |          |\n", "|    actor_loss      | 6.33     |\n", "|    critic_loss     | 0.594    |\n", "|    ent_coef        | 0.0065   |\n", "|    ent_coef_loss   | -0.369   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 171650   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.29e+03 |\n", "|    ep_rew_mean     | -33.8    |\n", "| time/              |          |\n", "|    episodes        | 256      |\n", "|    fps             | 161      |\n", "|    time_elapsed    | 1101     |\n", "|    total_timesteps | 178078   |\n", "| train/             |          |\n", "|    actor_loss      | 3.16     |\n", "|    critic_loss     | 0.326    |\n", "|    ent_coef        | 0.00791  |\n", "|    ent_coef_loss   | -0.85    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 177977   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.35e+03 |\n", "|    ep_rew_mean     | -18.6    |\n", "| time/              |          |\n", "|    episodes        | 260      |\n", "|    fps             | 160      |\n", "|    time_elapsed    | 1145     |\n", "|    total_timesteps | 184120   |\n", "| train/             |          |\n", "|    actor_loss      | 1.95     |\n", "|    critic_loss     | 0.324    |\n", "|    ent_coef        | 0.00795  |\n", "|    ent_coef_loss   | -1.38    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 184019   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.37e+03 |\n", "|    ep_rew_mean     | -4.23    |\n", "| time/              |          |\n", "|    episodes        | 264      |\n", "|    fps             | 160      |\n", "|    time_elapsed    | 1183     |\n", "|    total_timesteps | 189961   |\n", "| train/             |          |\n", "|    actor_loss      | -0.199   |\n", "|    critic_loss     | 0.426    |\n", "|    ent_coef        | 0.00832  |\n", "|    ent_coef_loss   | 0.326    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 189860   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.38e+03 |\n", "|    ep_rew_mean     | 10.8     |\n", "| time/              |          |\n", "|    episodes        | 268      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1223     |\n", "|    total_timesteps | 195487   |\n", "| train/             |          |\n", "|    actor_loss      | -0.323   |\n", "|    critic_loss     | 0.313    |\n", "|    ent_coef        | 0.00872  |\n", "|    ent_coef_loss   | 0.107    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 195386   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.36e+03 |\n", "|    ep_rew_mean     | 25.8     |\n", "| time/              |          |\n", "|    episodes        | 272      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1256     |\n", "|    total_timesteps | 200801   |\n", "| train/             |          |\n", "|    actor_loss      | -0.487   |\n", "|    critic_loss     | 0.22     |\n", "|    ent_coef        | 0.00897  |\n", "|    ent_coef_loss   | 0.999    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 200700   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.35e+03 |\n", "|    ep_rew_mean     | 37.1     |\n", "| time/              |          |\n", "|    episodes        | 276      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1286     |\n", "|    total_timesteps | 205356   |\n", "| train/             |          |\n", "|    actor_loss      | -1.02    |\n", "|    critic_loss     | 0.258    |\n", "|    ent_coef        | 0.00864  |\n", "|    ent_coef_loss   | 2.03     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 205255   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.33e+03 |\n", "|    ep_rew_mean     | 51.4     |\n", "| time/              |          |\n", "|    episodes        | 280      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1320     |\n", "|    total_timesteps | 210514   |\n", "| train/             |          |\n", "|    actor_loss      | -2.37    |\n", "|    critic_loss     | 0.266    |\n", "|    ent_coef        | 0.00915  |\n", "|    ent_coef_loss   | 0.0552   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 210413   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.32e+03 |\n", "|    ep_rew_mean     | 64       |\n", "| time/              |          |\n", "|    episodes        | 284      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1350     |\n", "|    total_timesteps | 215272   |\n", "| train/             |          |\n", "|    actor_loss      | -3.74    |\n", "|    critic_loss     | 0.342    |\n", "|    ent_coef        | 0.00896  |\n", "|    ent_coef_loss   | -1.37    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 215171   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.29e+03 |\n", "|    ep_rew_mean     | 74.1     |\n", "| time/              |          |\n", "|    episodes        | 288      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1376     |\n", "|    total_timesteps | 219263   |\n", "| train/             |          |\n", "|    actor_loss      | -3.02    |\n", "|    critic_loss     | 29.6     |\n", "|    ent_coef        | 0.00939  |\n", "|    ent_coef_loss   | 0.929    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 219162   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.27e+03 |\n", "|    ep_rew_mean     | 80.7     |\n", "| time/              |          |\n", "|    episodes        | 292      |\n", "|    fps             | 158      |\n", "|    time_elapsed    | 1395     |\n", "|    total_timesteps | 221858   |\n", "| train/             |          |\n", "|    actor_loss      | -2.95    |\n", "|    critic_loss     | 0.385    |\n", "|    ent_coef        | 0.00876  |\n", "|    ent_coef_loss   | 1.52     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 221757   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.25e+03 |\n", "|    ep_rew_mean     | 90.3     |\n", "| time/              |          |\n", "|    episodes        | 296      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1418     |\n", "|    total_timesteps | 225585   |\n", "| train/             |          |\n", "|    actor_loss      | -5.56    |\n", "|    critic_loss     | 0.188    |\n", "|    ent_coef        | 0.00898  |\n", "|    ent_coef_loss   | -0.595   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 225484   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.28e+03 |\n", "|    ep_rew_mean     | 105      |\n", "| time/              |          |\n", "|    episodes        | 300      |\n", "|    fps             | 158      |\n", "|    time_elapsed    | 1450     |\n", "|    total_timesteps | 230439   |\n", "| train/             |          |\n", "|    actor_loss      | -5.06    |\n", "|    critic_loss     | 0.294    |\n", "|    ent_coef        | 0.00933  |\n", "|    ent_coef_loss   | -1.39    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 230338   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.27e+03 |\n", "|    ep_rew_mean     | 119      |\n", "| time/              |          |\n", "|    episodes        | 304      |\n", "|    fps             | 158      |\n", "|    time_elapsed    | 1481     |\n", "|    total_timesteps | 235161   |\n", "| train/             |          |\n", "|    actor_loss      | -6.3     |\n", "|    critic_loss     | 0.171    |\n", "|    ent_coef        | 0.00897  |\n", "|    ent_coef_loss   | -2.5     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 235060   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.25e+03 |\n", "|    ep_rew_mean     | 132      |\n", "| time/              |          |\n", "|    episodes        | 308      |\n", "|    fps             | 158      |\n", "|    time_elapsed    | 1508     |\n", "|    total_timesteps | 239604   |\n", "| train/             |          |\n", "|    actor_loss      | -6.54    |\n", "|    critic_loss     | 0.368    |\n", "|    ent_coef        | 0.00876  |\n", "|    ent_coef_loss   | -0.404   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 239503   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.24e+03 |\n", "|    ep_rew_mean     | 147      |\n", "| time/              |          |\n", "|    episodes        | 312      |\n", "|    fps             | 158      |\n", "|    time_elapsed    | 1538     |\n", "|    total_timesteps | 244346   |\n", "| train/             |          |\n", "|    actor_loss      | -7.15    |\n", "|    critic_loss     | 0.114    |\n", "|    ent_coef        | 0.00904  |\n", "|    ent_coef_loss   | 0.199    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 244245   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.27e+03 |\n", "|    ep_rew_mean     | 162      |\n", "| time/              |          |\n", "|    episodes        | 316      |\n", "|    fps             | 158      |\n", "|    time_elapsed    | 1566     |\n", "|    total_timesteps | 249081   |\n", "| train/             |          |\n", "|    actor_loss      | -7.96    |\n", "|    critic_loss     | 0.258    |\n", "|    ent_coef        | 0.00934  |\n", "|    ent_coef_loss   | -0.0948  |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 248980   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.27e+03 |\n", "|    ep_rew_mean     | 176      |\n", "| time/              |          |\n", "|    episodes        | 320      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1594     |\n", "|    total_timesteps | 253737   |\n", "| train/             |          |\n", "|    actor_loss      | -7.84    |\n", "|    critic_loss     | 0.13     |\n", "|    ent_coef        | 0.00924  |\n", "|    ent_coef_loss   | -0.166   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 253636   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.28e+03 |\n", "|    ep_rew_mean     | 187      |\n", "| time/              |          |\n", "|    episodes        | 324      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1623     |\n", "|    total_timesteps | 258451   |\n", "| train/             |          |\n", "|    actor_loss      | -7.45    |\n", "|    critic_loss     | 0.259    |\n", "|    ent_coef        | 0.00885  |\n", "|    ent_coef_loss   | 0.726    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 258350   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.27e+03 |\n", "|    ep_rew_mean     | 198      |\n", "| time/              |          |\n", "|    episodes        | 328      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1651     |\n", "|    total_timesteps | 263172   |\n", "| train/             |          |\n", "|    actor_loss      | -8.39    |\n", "|    critic_loss     | 0.387    |\n", "|    ent_coef        | 0.00931  |\n", "|    ent_coef_loss   | -0.798   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 263071   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.27e+03 |\n", "|    ep_rew_mean     | 213      |\n", "| time/              |          |\n", "|    episodes        | 332      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1680     |\n", "|    total_timesteps | 267833   |\n", "| train/             |          |\n", "|    actor_loss      | -9.15    |\n", "|    critic_loss     | 0.251    |\n", "|    ent_coef        | 0.00935  |\n", "|    ent_coef_loss   | -0.317   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 267732   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.25e+03 |\n", "|    ep_rew_mean     | 227      |\n", "| time/              |          |\n", "|    episodes        | 336      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1709     |\n", "|    total_timesteps | 272419   |\n", "| train/             |          |\n", "|    actor_loss      | -7.74    |\n", "|    critic_loss     | 1.35     |\n", "|    ent_coef        | 0.00902  |\n", "|    ent_coef_loss   | 0.557    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 272318   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.23e+03 |\n", "|    ep_rew_mean     | 240      |\n", "| time/              |          |\n", "|    episodes        | 340      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1736     |\n", "|    total_timesteps | 276947   |\n", "| train/             |          |\n", "|    actor_loss      | -8.87    |\n", "|    critic_loss     | 0.613    |\n", "|    ent_coef        | 0.00931  |\n", "|    ent_coef_loss   | 0.95     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 276846   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.22e+03 |\n", "|    ep_rew_mean     | 252      |\n", "| time/              |          |\n", "|    episodes        | 344      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1763     |\n", "|    total_timesteps | 281253   |\n", "| train/             |          |\n", "|    actor_loss      | -9.5     |\n", "|    critic_loss     | 0.108    |\n", "|    ent_coef        | 0.00935  |\n", "|    ent_coef_loss   | 0.307    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 281152   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.2e+03  |\n", "|    ep_rew_mean     | 257      |\n", "| time/              |          |\n", "|    episodes        | 348      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1788     |\n", "|    total_timesteps | 285624   |\n", "| train/             |          |\n", "|    actor_loss      | -10.4    |\n", "|    critic_loss     | 0.148    |\n", "|    ent_coef        | 0.0092   |\n", "|    ent_coef_loss   | -1.01    |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 285523   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.18e+03 |\n", "|    ep_rew_mean     | 262      |\n", "| time/              |          |\n", "|    episodes        | 352      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1814     |\n", "|    total_timesteps | 289933   |\n", "| train/             |          |\n", "|    actor_loss      | -10.4    |\n", "|    critic_loss     | 0.0931   |\n", "|    ent_coef        | 0.0095   |\n", "|    ent_coef_loss   | -0.715   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 289832   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.16e+03 |\n", "|    ep_rew_mean     | 263      |\n", "| time/              |          |\n", "|    episodes        | 356      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1839     |\n", "|    total_timesteps | 294073   |\n", "| train/             |          |\n", "|    actor_loss      | -10.1    |\n", "|    critic_loss     | 0.166    |\n", "|    ent_coef        | 0.00905  |\n", "|    ent_coef_loss   | 2.94     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 293972   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.14e+03 |\n", "|    ep_rew_mean     | 265      |\n", "| time/              |          |\n", "|    episodes        | 360      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1868     |\n", "|    total_timesteps | 298238   |\n", "| train/             |          |\n", "|    actor_loss      | -9.32    |\n", "|    critic_loss     | 16.1     |\n", "|    ent_coef        | 0.00919  |\n", "|    ent_coef_loss   | 1.04     |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 298137   |\n", "---------------------------------\n", "---------------------------------\n", "| rollout/           |          |\n", "|    ep_len_mean     | 1.12e+03 |\n", "|    ep_rew_mean     | 266      |\n", "| time/              |          |\n", "|    episodes        | 364      |\n", "|    fps             | 159      |\n", "|    time_elapsed    | 1895     |\n", "|    total_timesteps | 302424   |\n", "| train/             |          |\n", "|    actor_loss      | -11.2    |\n", "|    critic_loss     | 0.221    |\n", "|    ent_coef        | 0.00926  |\n", "|    ent_coef_loss   | -0.129   |\n", "|    learning_rate   | 0.0003   |\n", "|    n_updates       | 302323   |\n", "---------------------------------\n"]}], "source": ["import gymnasium as gym\n", "from stable_baselines3 import SAC\n", "\n", "env = gym.make(env_id)\n", "model = SAC(\"MlpPolicy\", env, verbose=1, tensorboard_log=log_dir)\n", "model.learn(total_timesteps=500_000)\n", "model.save(model_fn)"]}, {"cell_type": "markdown", "id": "f3c528b3-c890-4d43-9854-6af07bfcc94f", "metadata": {"pycharm": {"name": "#%% md\n"}}, "source": ["# Rollout"]}, {"cell_type": "markdown", "id": "a7383350", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> Trained and Random Agent"]}, {"cell_type": "code", "execution_count": null, "id": "c0cea3d9", "metadata": {}, "outputs": [], "source": ["import gymnasium as gym\n", "import numpy as np\n", "from stable_baselines3.common.vec_env import DummyVecEnv\n", "from stable_baselines3.common.evaluation import evaluate_policy\n", "from stable_baselines3.common.monitor import Monitor\n", "from stable_baselines3 import SAC\n", "\n", "\n", "n_eval_episodes = 10\n", "\n", "\n", "# Create/ load agents\n", "\n", "# Untrained agent\n", "env = gym.make(env_id)\n", "untrained_agent = SAC(\"MlpPolicy\", env, verbose=0)\n", "\n", "# Trained agents\n", "trained_agent = SAC.load(model_fn)\n", "\n", "agents = {\n", "    \"untrained\": untrained_agent,\n", "    \"trained\": trained_agent,\n", "}\n", "\n", "# Create env to evaluate\n", "env = DummyVecEnv([lambda: gym.make(env_id)])\n", "env = Monitor(gym.make(env_id))\n", "\n", "# Evaluate each agent and gather results\n", "results = []\n", "for name, agent in agents.items():\n", "    # Rollout n_eval_episodes and record performance\n", "    means, stds = evaluate_policy(\n", "        agent, env, n_eval_episodes=n_eval_episodes, return_episode_rewards=True\n", "    )\n", "    performance = np.mean(means)\n", "    results.append(\n", "        pd.DataFrame(\n", "            {\n", "                \"agent\": name,\n", "                \"episode\": np.arange(0, n_eval_episodes),\n", "                \"reward\": means,\n", "                \"length\": stds,\n", "            }\n", "        )\n", "    )\n", "results = pd.concat(results).reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "a876b14e", "metadata": {}, "outputs": [], "source": ["from rich import print as printr\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "df = results\n", "printr(df)\n", "\n", "ax = sns.boxplot(data=df, x=\"agent\", y=\"reward\")\n", "plt.show()\n", "\n", "sns.boxplot(data=df, x=\"agent\", y=\"length\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "7fb27b941602401d91542211134fc71a", "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["video_folder = \"logs/videos/\"\n", "video_length = 1000"]}, {"cell_type": "markdown", "id": "acae54e37e7d407bbb7b55eff062a284", "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}, "source": ["## Record random agent"]}, {"cell_type": "code", "execution_count": null, "id": "9a63283cbaf04dbcab1f6479b197f3a8", "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["import gymnasium as gym\n", "from stable_baselines3.common.vec_env import VecVideoRecorder, DummyVecEnv\n", "\n", "# This creates a vectorized env (here of length 1). Is useful for parallelization\n", "# when we have several workers that can perform individual rollouts.\n", "env = DummyVecEnv([lambda: gym.make(env_id, render_mode=\"rgb_array\")])\n", "\n", "env = VecVideoRecorder(\n", "    env,\n", "    video_folder,\n", "    record_video_trigger=lambda x: x == 0,\n", "    video_length=video_length,\n", "    name_prefix=f\"random-agent-{env_id}\",\n", ")\n", "\n", "n_steps = video_length\n", "\n", "# Reset and initialize the environment\n", "obs = env.reset()\n", "\n", "# For the number of frames\n", "for i in range(n_steps):\n", "    # Sample a random action from the action space\n", "    action = [env.action_space.sample()]\n", "\n", "    # Step the environment\n", "    obs, reward, done, info = env.step(action)\n", "    if done:\n", "        # If the env signals the end of the episode, stop recording\n", "        break\n", "        obs = env.reset()\n", "# Close / cleanup env\n", "env.close()"]}, {"cell_type": "markdown", "id": "8dd0d8092fe74a7c96281538738b07e2", "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}, "source": ["## Record trained agent"]}, {"cell_type": "code", "execution_count": null, "id": "72eea5119410473aa328ad9291626812", "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n"}}, "outputs": [], "source": ["from stable_baselines3 import SAC\n", "import gymnasium as gym\n", "from stable_baselines3.common.vec_env import VecVideoRecorder, DummyVecEnv\n", "\n", "env = DummyVecEnv([lambda: gym.make(env_id, render_mode=\"rgb_array\")])\n", "\n", "# Record the video starting at the first step\n", "env = VecVideoRecorder(\n", "    env,\n", "    video_folder,\n", "    record_video_trigger=lambda x: x == 0,\n", "    video_length=video_length,\n", "    name_prefix=f\"trained-agent-{env_id}\",\n", ")\n", "\n", "model = SAC.load(model_fn)\n", "\n", "n_steps = video_length\n", "\n", "obs = env.reset()\n", "for i in range(n_steps):\n", "    # Now PREDICT the action to take with the trained model 🤖\n", "    action, _states = model.predict(obs, deterministic=True)\n", "    obs, reward, done, info = env.step(action)\n", "    if done:\n", "        break\n", "        obs = env.reset()\n", "env.close()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 5}