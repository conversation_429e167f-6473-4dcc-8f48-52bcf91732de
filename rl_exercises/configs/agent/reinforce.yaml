# configs/reinforce.yaml

# Gymnasium environment settings
env:
  name: CartPole-v1

# Random seed for reproducibility
seed: 0

# Agent hyperparameters
agent:
  lr: 1e-2
  gamma: 0.99
  hidden_size: 128

# Training settings
train:
  episodes: 1000        # total episodes to train
  eval_interval: 50     # how often (in episodes) to run evaluation
  eval_episodes: 5      # number of episodes to evaluate
