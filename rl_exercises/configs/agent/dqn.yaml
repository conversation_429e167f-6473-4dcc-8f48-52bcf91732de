# @package _global_
env:
  name: CartPole-v1

seed: 0

agent:
  buffer_capacity:    10000    # max replay buffer size
  batch_size:         32       # minibatch size
  learning_rate:      0.001    # maps to DQNAgent’s lr
  gamma:              0.99
  epsilon_start:      1.0
  epsilon_final:      0.01
  epsilon_decay:      500
  target_update_freq: 1000

train:
  num_frames:     20000   # total env steps
  eval_interval:  1000    # print avg reward every this many episodes