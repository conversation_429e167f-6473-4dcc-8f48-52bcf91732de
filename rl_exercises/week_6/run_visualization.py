#!/usr/bin/env python3
"""
Simple script to run the Actor-Critic performance visualization.

This script provides a quick way to train and visualize an Actor-Critic agent
on the Lunar Lander environment.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from rl_exercises.week_6.visualize_performance import PerformanceVisualizer


def quick_demo():
    """Run a quick demonstration of the Actor-Critic agent."""
    print("🚀 Actor-Critic Lunar Lander Visualization Demo")
    print("=" * 50)
    
    # Initialize visualizer
    visualizer = PerformanceVisualizer(env_name="LunarLander-v3", seed=42)
    
    print("\n📚 Training a quick demo agent...")
    print("This will train for 20,000 steps (should take 2-3 minutes)")
    
    # Train agent with reduced steps for demo
    agent = visualizer.train_agent(
        total_steps=20000,
        eval_interval=5000,
        eval_episodes=3,
        baseline_type="value",
        lr_actor=5e-3,
        lr_critic=1e-2
    )
    
    print("\n📊 Generating training performance plots...")
    visualizer.plot_training_performance()
    
    print("\n🎯 Evaluating agent performance...")
    visualizer.evaluate_agent_performance(agent, num_episodes=30)
    
    print("\n📈 Generating performance distribution plots...")
    visualizer.plot_performance_distribution(agent, num_episodes=30)
    
    # Ask if user wants to see live gameplay
    print("\n🎮 Would you like to see the agent play live?")
    show_gameplay = input("Enter 'y' to watch the agent play, or any other key to skip: ").strip().lower()
    
    if show_gameplay == 'y':
        print("\n🎬 Starting live gameplay visualization...")
        print("Close the game window to continue to the next episode.")
        visualizer.visualize_agent_gameplay(agent, num_episodes=3)
    
    print(f"\n✅ All outputs saved to: {visualizer.output_dir}")
    print("\nDemo complete! Check the generated plots to see how well your agent learned.")


def full_training():
    """Run full training with the standard configuration."""
    print("🚀 Actor-Critic Lunar Lander Full Training")
    print("=" * 50)
    
    # Initialize visualizer
    visualizer = PerformanceVisualizer(env_name="LunarLander-v3", seed=42)
    
    print("\n📚 Training agent with full configuration...")
    print("This will train for 200,000 steps (may take 15-20 minutes)")
    
    # Train agent with full configuration
    agent = visualizer.train_agent(
        total_steps=200000,
        eval_interval=10000,
        eval_episodes=5,
        baseline_type="value",
        lr_actor=5e-3,
        lr_critic=1e-2
    )
    
    print("\n📊 Generating comprehensive performance analysis...")
    visualizer.plot_training_performance()
    
    print("\n🎯 Evaluating agent performance...")
    visualizer.evaluate_agent_performance(agent, num_episodes=100)
    
    print("\n📈 Generating performance distribution plots...")
    visualizer.plot_performance_distribution(agent, num_episodes=100)
    
    # Ask if user wants to see live gameplay
    print("\n🎮 Would you like to see the agent play live?")
    show_gameplay = input("Enter 'y' to watch the agent play, or any other key to skip: ").strip().lower()
    
    if show_gameplay == 'y':
        print("\n🎬 Starting live gameplay visualization...")
        print("Close the game window to continue to the next episode.")
        visualizer.visualize_agent_gameplay(agent, num_episodes=5)
    
    print(f"\n✅ All outputs saved to: {visualizer.output_dir}")
    print("\nTraining complete! Check the generated plots to analyze performance.")


def main():
    """Main function with user choice."""
    print("Actor-Critic Performance Visualization")
    print("=" * 40)
    print("\nChoose an option:")
    print("1. Quick demo (20k steps, ~3 minutes)")
    print("2. Full training (200k steps, ~20 minutes)")
    print("3. Interactive mode (custom options)")
    
    choice = input("\nEnter your choice (1/2/3): ").strip()
    
    if choice == "1":
        quick_demo()
    elif choice == "2":
        full_training()
    elif choice == "3":
        # Import and run the interactive main function
        from rl_exercises.week_6.visualize_performance import main as interactive_main
        interactive_main()
    else:
        print("Invalid choice. Running quick demo...")
        quick_demo()


if __name__ == "__main__":
    main()
