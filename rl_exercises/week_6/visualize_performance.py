"""
Visualization script for Actor-Critic agent performance on Lunar Lander environment.

This script provides comprehensive visualization of the trained model including:
- Training performance plots
- Live gameplay visualization
- Performance statistics
- Model evaluation metrics
"""

import os
import sys
import time
import pickle
from typing import List, Tuple, Dict, Any
from pathlib import Path

import gymnasium as gym
import numpy as np
import torch
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Rectangle
import seaborn as sns
from datetime import datetime

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from rl_exercises.week_6.actor_critic import ActorCriticAgent, set_seed
from rl_exercises.week_6.networks import Policy, ValueNetwork


class PerformanceVisualizer:
    """
    Comprehensive visualization tool for Actor-Critic agent performance.
    """

    def __init__(self, env_name: str = "LunarLander-v3", seed: int = 0):
        """
        Initialize the visualizer.

        Parameters
        ----------
        env_name : str
            Name of the environment to visualize
        seed : int
            Random seed for reproducibility
        """
        self.env_name = env_name
        self.seed = seed
        self.env = gym.make(env_name, render_mode="rgb_array")
        self.eval_env = gym.make(env_name, render_mode="human")
        set_seed(self.env, seed)

        # Training history storage
        self.training_history = {
            'steps': [],
            'returns': [],
            'policy_losses': [],
            'value_losses': [],
            'eval_returns': [],
            'eval_steps': [],
            'eval_stds': []
        }

        # Create output directory
        self.output_dir = Path("outputs") / datetime.now().strftime("%Y-%m-%d") / datetime.now().strftime("%H-%M-%S")
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def train_agent(self,
                   total_steps: int = 200000,
                   eval_interval: int = 10000,
                   eval_episodes: int = 5,
                   baseline_type: str = "value",
                   lr_actor: float = 5e-3,
                   lr_critic: float = 1e-2,
                   gamma: float = 0.99,
                   gae_lambda: float = 0.95,
                   hidden_size: int = 128,
                   save_model: bool = True) -> ActorCriticAgent:
        """
        Train an Actor-Critic agent and record training history.

        Parameters
        ----------
        total_steps : int
            Total number of training steps
        eval_interval : int
            Interval between evaluations
        eval_episodes : int
            Number of episodes for evaluation
        baseline_type : str
            Type of baseline ('none', 'avg', 'value', 'gae')
        lr_actor : float
            Learning rate for actor
        lr_critic : float
            Learning rate for critic
        gamma : float
            Discount factor
        gae_lambda : float
            GAE lambda parameter
        hidden_size : int
            Hidden layer size
        save_model : bool
            Whether to save the trained model

        Returns
        -------
        agent : ActorCriticAgent
            Trained agent
        """
        print(f"Training Actor-Critic agent on {self.env_name}...")
        print(f"Configuration: baseline={baseline_type}, lr_actor={lr_actor}, lr_critic={lr_critic}")

        # Create agent
        agent = ActorCriticAgent(
            self.env,
            lr_actor=lr_actor,
            lr_critic=lr_critic,
            gamma=gamma,
            gae_lambda=gae_lambda,
            seed=self.seed,
            hidden_size=hidden_size,
            baseline_type=baseline_type
        )

        # Custom training loop with history tracking
        eval_env = gym.make(self.env.spec.id)
        step_count = 0

        print("Starting training...")
        start_time = time.time()

        while step_count < total_steps:
            state, _ = self.env.reset()
            done = False
            trajectory = []

            while not done and step_count < total_steps:
                action, logp = agent.predict_action(state)
                next_state, reward, term, trunc, _ = self.env.step(action)
                done = term or trunc
                trajectory.append((state, action, float(reward), next_state, done, logp))
                state = next_state
                step_count += 1

                # Evaluation
                if step_count % eval_interval == 0:
                    mean_r, std_r = agent.evaluate(eval_env, num_episodes=eval_episodes)
                    self.training_history['eval_steps'].append(step_count)
                    self.training_history['eval_returns'].append(mean_r)
                    self.training_history['eval_stds'].append(std_r)

                    elapsed_time = time.time() - start_time
                    print(f"[Eval ] Step {step_count:6d} | Return {mean_r:6.1f} ± {std_r:4.1f} | "
                          f"Time: {elapsed_time:.1f}s")

            # Update agent and record losses
            policy_loss, value_loss = agent.update_agent(trajectory)
            total_return = sum(r for _, _, r, *_ in trajectory)

            self.training_history['steps'].append(step_count)
            self.training_history['returns'].append(total_return)
            self.training_history['policy_losses'].append(policy_loss)
            self.training_history['value_losses'].append(value_loss)

            if step_count % 5000 == 0:
                print(f"[Train] Step {step_count:6d} | Return {total_return:6.1f} | "
                      f"Policy Loss {policy_loss:.3f} | Value Loss {value_loss:.3f}")

        total_time = time.time() - start_time
        print(f"\nTraining completed in {total_time:.1f} seconds!")

        # Save model and training history
        if save_model:
            self.save_agent_and_history(agent)

        return agent

    def save_agent_and_history(self, agent: ActorCriticAgent):
        """Save the trained agent and training history."""
        # Save model state dict
        model_path = self.output_dir / "trained_model.pth"
        torch.save({
            'policy_state_dict': agent.policy.state_dict(),
            'value_state_dict': agent.value_fn.state_dict() if hasattr(agent, 'value_fn') else None,
            'training_config': {
                'env_name': self.env_name,
                'seed': self.seed,
                'baseline_type': agent.baseline_type,
                'gamma': agent.gamma,
                'gae_lambda': agent.gae_lambda
            }
        }, model_path)

        # Save training history
        history_path = self.output_dir / "training_history.pkl"
        with open(history_path, 'wb') as f:
            pickle.dump(self.training_history, f)

        print(f"Model and history saved to {self.output_dir}")

    def load_agent_and_history(self, model_path: str, history_path: str = None) -> ActorCriticAgent:
        """Load a trained agent and optionally its training history."""
        checkpoint = torch.load(model_path)
        config = checkpoint['training_config']

        # Create agent with saved configuration
        agent = ActorCriticAgent(
            self.env,
            baseline_type=config['baseline_type'],
            gamma=config['gamma'],
            gae_lambda=config['gae_lambda'],
            seed=config['seed']
        )

        # Load model weights
        agent.policy.load_state_dict(checkpoint['policy_state_dict'])
        if checkpoint['value_state_dict'] is not None and hasattr(agent, 'value_fn'):
            agent.value_fn.load_state_dict(checkpoint['value_state_dict'])

        # Load training history if provided
        if history_path and os.path.exists(history_path):
            with open(history_path, 'rb') as f:
                self.training_history = pickle.load(f)

        print(f"Model loaded from {model_path}")
        return agent

    def plot_training_performance(self, save_plot: bool = True, show_plot: bool = True):
        """
        Create comprehensive training performance plots.

        Parameters
        ----------
        save_plot : bool
            Whether to save the plot
        show_plot : bool
            Whether to display the plot
        """
        if not self.training_history['steps']:
            print("No training history available. Train an agent first.")
            return

        # Set up the plot style
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'Actor-Critic Training Performance on {self.env_name}', fontsize=16, fontweight='bold')

        # Plot 1: Episode Returns
        ax1 = axes[0, 0]
        steps = self.training_history['steps']
        returns = self.training_history['returns']

        # Smooth the returns for better visualization
        if len(returns) > 50:
            window_size = min(50, len(returns) // 10)
            smoothed_returns = np.convolve(returns, np.ones(window_size)/window_size, mode='valid')
            smoothed_steps = steps[window_size-1:]
            ax1.plot(smoothed_steps, smoothed_returns, 'b-', linewidth=2, label=f'Smoothed (window={window_size})')

        ax1.plot(steps, returns, 'lightblue', alpha=0.3, label='Raw returns')
        ax1.set_xlabel('Training Steps')
        ax1.set_ylabel('Episode Return')
        ax1.set_title('Training Episode Returns')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Plot 2: Evaluation Returns
        ax2 = axes[0, 1]
        if self.training_history['eval_steps']:
            eval_steps = self.training_history['eval_steps']
            eval_returns = self.training_history['eval_returns']
            eval_stds = self.training_history['eval_stds']

            ax2.errorbar(eval_steps, eval_returns, yerr=eval_stds,
                        fmt='o-', linewidth=2, markersize=6, capsize=5,
                        color='green', label='Evaluation Returns')
            ax2.fill_between(eval_steps,
                           np.array(eval_returns) - np.array(eval_stds),
                           np.array(eval_returns) + np.array(eval_stds),
                           alpha=0.2, color='green')

        ax2.set_xlabel('Training Steps')
        ax2.set_ylabel('Average Return')
        ax2.set_title('Evaluation Performance')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Plot 3: Policy Loss
        ax3 = axes[1, 0]
        policy_losses = self.training_history['policy_losses']
        if len(policy_losses) > 50:
            window_size = min(50, len(policy_losses) // 10)
            smoothed_losses = np.convolve(policy_losses, np.ones(window_size)/window_size, mode='valid')
            smoothed_steps = steps[window_size-1:]
            ax3.plot(smoothed_steps, smoothed_losses, 'r-', linewidth=2, label=f'Smoothed (window={window_size})')

        ax3.plot(steps, policy_losses, 'lightcoral', alpha=0.3, label='Raw policy loss')
        ax3.set_xlabel('Training Steps')
        ax3.set_ylabel('Policy Loss')
        ax3.set_title('Policy Network Loss')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # Plot 4: Value Loss
        ax4 = axes[1, 1]
        value_losses = self.training_history['value_losses']
        if len(value_losses) > 50:
            window_size = min(50, len(value_losses) // 10)
            smoothed_losses = np.convolve(value_losses, np.ones(window_size)/window_size, mode='valid')
            smoothed_steps = steps[window_size-1:]
            ax4.plot(smoothed_steps, smoothed_losses, 'purple', linewidth=2, label=f'Smoothed (window={window_size})')

        ax4.plot(steps, value_losses, 'plum', alpha=0.3, label='Raw value loss')
        ax4.set_xlabel('Training Steps')
        ax4.set_ylabel('Value Loss')
        ax4.set_title('Value Network Loss')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plot:
            plot_path = self.output_dir / "training_performance.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"Training performance plot saved to {plot_path}")

        if show_plot:
            plt.show()
        else:
            plt.close()

    def evaluate_agent_performance(self, agent: ActorCriticAgent, num_episodes: int = 100) -> Dict[str, float]:
        """
        Comprehensive evaluation of agent performance.

        Parameters
        ----------
        agent : ActorCriticAgent
            Trained agent to evaluate
        num_episodes : int
            Number of episodes for evaluation

        Returns
        -------
        stats : dict
            Performance statistics
        """
        print(f"Evaluating agent performance over {num_episodes} episodes...")

        returns = []
        episode_lengths = []
        success_count = 0  # Episodes with return > 200 (successful landing)

        for episode in range(num_episodes):
            state, _ = self.env.reset()
            done = False
            total_return = 0.0
            steps = 0

            while not done:
                action, _ = agent.predict_action(state, evaluate=True)
                state, reward, term, trunc, _ = self.env.step(action)
                done = term or trunc
                total_return += reward
                steps += 1

                if steps > 1000:  # Prevent infinite episodes
                    break

            returns.append(total_return)
            episode_lengths.append(steps)

            if total_return > 200:  # Successful landing threshold
                success_count += 1

            if (episode + 1) % 20 == 0:
                print(f"Completed {episode + 1}/{num_episodes} episodes")

        # Calculate statistics
        stats = {
            'mean_return': np.mean(returns),
            'std_return': np.std(returns),
            'min_return': np.min(returns),
            'max_return': np.max(returns),
            'median_return': np.median(returns),
            'success_rate': success_count / num_episodes,
            'mean_episode_length': np.mean(episode_lengths),
            'std_episode_length': np.std(episode_lengths)
        }

        # Print results
        print("\n" + "="*50)
        print("AGENT PERFORMANCE EVALUATION")
        print("="*50)
        print(f"Episodes evaluated: {num_episodes}")
        print(f"Mean return: {stats['mean_return']:.2f} ± {stats['std_return']:.2f}")
        print(f"Return range: [{stats['min_return']:.2f}, {stats['max_return']:.2f}]")
        print(f"Median return: {stats['median_return']:.2f}")
        print(f"Success rate: {stats['success_rate']:.1%} (return > 200)")
        print(f"Mean episode length: {stats['mean_episode_length']:.1f} ± {stats['std_episode_length']:.1f}")
        print("="*50)

        return stats

    def create_gameplay_gif(self, agent: ActorCriticAgent, num_episodes: int = 1, fps: int = 30) -> str:
        """
        Create a GIF of the agent playing the environment.

        Parameters
        ----------
        agent : ActorCriticAgent
            Trained agent to record
        num_episodes : int
            Number of episodes to record
        fps : int
            Frames per second for the GIF

        Returns
        -------
        gif_path : str
            Path to the saved GIF file
        """
        print(f"Creating GIF of agent gameplay for {num_episodes} episode(s)...")

        all_frames = []

        for episode in range(num_episodes):
            print(f"Recording episode {episode + 1}/{num_episodes}...")
            state, _ = self.env.reset()
            done = False
            total_return = 0.0
            steps = 0
            episode_frames = []

            while not done:
                # Capture frame
                frame = self.env.render()
                episode_frames.append(frame)

                action, _ = agent.predict_action(state, evaluate=True)
                state, reward, term, trunc, _ = self.env.step(action)
                done = term or trunc
                total_return += reward
                steps += 1

                if steps > 1000:  # Prevent infinite episodes
                    break

            print(f"Episode {episode + 1} completed: Return = {total_return:.2f}, Steps = {steps}")
            all_frames.extend(episode_frames)

            # Add a few frames of pause between episodes if multiple episodes
            if num_episodes > 1 and episode < num_episodes - 1:
                pause_frames = 10
                for _ in range(pause_frames):
                    all_frames.append(episode_frames[-1])  # Repeat last frame

        # Save as GIF
        try:
            import imageio
        except ImportError:
            print("Error: imageio not installed. Install with: pip install imageio")
            return None

        gif_path = self.output_dir / f"lunar_lander_gameplay_{num_episodes}ep.gif"
        imageio.mimsave(gif_path, all_frames, fps=fps)
        print(f"GIF saved to: {gif_path}")
        print(f"GIF contains {len(all_frames)} frames at {fps} FPS")

        return str(gif_path)

    def visualize_agent_gameplay(self, agent: ActorCriticAgent, num_episodes: int = 3, save_gif: bool = False):
        """
        Visualize the agent playing the environment.

        Parameters
        ----------
        agent : ActorCriticAgent
            Trained agent to visualize
        num_episodes : int
            Number of episodes to show
        save_gif : bool
            Whether to save a GIF of the gameplay
        """
        print(f"Visualizing agent gameplay for {num_episodes} episodes...")
        print("Close the window to continue to the next episode.")

        for episode in range(num_episodes):
            print(f"\nEpisode {episode + 1}/{num_episodes}")
            state, _ = self.eval_env.reset()
            done = False
            total_return = 0.0
            steps = 0

            while not done:
                action, _ = agent.predict_action(state, evaluate=True)
                state, reward, term, trunc, _ = self.eval_env.step(action)
                done = term or trunc
                total_return += reward
                steps += 1

                # Add a small delay to make it easier to watch
                time.sleep(0.02)

                if steps > 1000:  # Prevent infinite episodes
                    break

            print(f"Episode {episode + 1} completed: Return = {total_return:.2f}, Steps = {steps}")

        self.eval_env.close()

        # Optionally create GIF
        if save_gif:
            print("\nCreating GIF of the gameplay...")
            self.create_gameplay_gif(agent, num_episodes=1, fps=30)

    def plot_performance_distribution(self, agent: ActorCriticAgent, num_episodes: int = 100,
                                    save_plot: bool = True, show_plot: bool = True):
        """
        Plot distribution of performance metrics.

        Parameters
        ----------
        agent : ActorCriticAgent
            Trained agent to evaluate
        num_episodes : int
            Number of episodes for evaluation
        save_plot : bool
            Whether to save the plot
        show_plot : bool
            Whether to display the plot
        """
        print(f"Generating performance distribution plots...")

        # Collect data
        returns = []
        episode_lengths = []

        for episode in range(num_episodes):
            state, _ = self.env.reset()
            done = False
            total_return = 0.0
            steps = 0

            while not done:
                action, _ = agent.predict_action(state, evaluate=True)
                state, reward, term, trunc, _ = self.env.step(action)
                done = term or trunc
                total_return += reward
                steps += 1

                if steps > 1000:
                    break

            returns.append(total_return)
            episode_lengths.append(steps)

        # Create plots
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        fig.suptitle(f'Performance Distribution Analysis ({num_episodes} episodes)', fontsize=14, fontweight='bold')

        # Returns distribution
        ax1 = axes[0]
        ax1.hist(returns, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.axvline(np.mean(returns), color='red', linestyle='--', linewidth=2, label=f'Mean: {np.mean(returns):.1f}')
        ax1.axvline(200, color='green', linestyle='--', linewidth=2, label='Success threshold: 200')
        ax1.set_xlabel('Episode Return')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Distribution of Episode Returns')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Episode lengths distribution
        ax2 = axes[1]
        ax2.hist(episode_lengths, bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
        ax2.axvline(np.mean(episode_lengths), color='red', linestyle='--', linewidth=2,
                   label=f'Mean: {np.mean(episode_lengths):.1f}')
        ax2.set_xlabel('Episode Length (steps)')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Distribution of Episode Lengths')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plot:
            plot_path = self.output_dir / "performance_distribution.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"Performance distribution plot saved to {plot_path}")

        if show_plot:
            plt.show()
        else:
            plt.close()


def main():
    """
    Main function to demonstrate the visualization capabilities.
    """
    print("Actor-Critic Performance Visualizer")
    print("===================================")

    # Initialize visualizer
    visualizer = PerformanceVisualizer(env_name="LunarLander-v3", seed=42)

    # Check if we should train a new agent or load existing one
    print("\nOptions:")
    print("1. Train a new agent")
    print("2. Load existing agent (if available)")
    print("3. Quick demo with pre-trained agent")

    choice = input("Enter your choice (1/2/3): ").strip()

    if choice == "1":
        # Train new agent
        print("\nTraining new agent...")
        agent = visualizer.train_agent(
            total_steps=50000,  # Reduced for demo
            eval_interval=5000,
            eval_episodes=5,
            baseline_type="value"
        )

        # Plot training performance
        visualizer.plot_training_performance()

    elif choice == "2":
        # Try to load existing agent
        model_path = input("Enter path to model file (.pth): ").strip()
        history_path = input("Enter path to history file (.pkl, optional): ").strip()

        try:
            agent = visualizer.load_agent_and_history(
                model_path,
                history_path if history_path else None
            )

            if visualizer.training_history['steps']:
                visualizer.plot_training_performance()

        except Exception as e:
            print(f"Error loading model: {e}")
            return

    elif choice == "3":
        # Quick demo - train a small agent
        print("\nTraining quick demo agent (this may take a few minutes)...")
        agent = visualizer.train_agent(
            total_steps=20000,
            eval_interval=5000,
            eval_episodes=3,
            baseline_type="value"
        )
        visualizer.plot_training_performance()

    else:
        print("Invalid choice. Exiting.")
        return

    # Evaluate agent performance
    print("\nEvaluating agent performance...")
    stats = visualizer.evaluate_agent_performance(agent, num_episodes=50)

    # Plot performance distribution
    visualizer.plot_performance_distribution(agent, num_episodes=50)

    # Ask if user wants to see live gameplay or create GIF
    print("\nGameplay options:")
    print("1. Watch live gameplay")
    print("2. Create GIF of gameplay")
    print("3. Both")
    print("4. Skip")

    gameplay_choice = input("Enter your choice (1/2/3/4): ").strip()

    if gameplay_choice in ['1', '3']:
        visualizer.visualize_agent_gameplay(agent, num_episodes=3)

    if gameplay_choice in ['2', '3']:
        print("\nCreating GIF of agent gameplay...")
        gif_path = visualizer.create_gameplay_gif(agent, num_episodes=1, fps=30)
        if gif_path:
            print(f"GIF created successfully: {gif_path}")

    print(f"\nAll outputs saved to: {visualizer.output_dir}")
    print("Visualization complete!")


if __name__ == "__main__":
    main()
