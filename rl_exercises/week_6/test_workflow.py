#!/usr/bin/env python3
"""
Test script for the CSV + RLiable workflow.

This script runs a minimal test to verify that the workflow works correctly.
"""

import sys
import tempfile
import shutil
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from rl_exercises.week_6.run_experiments import ExperimentRunner
from rl_exercises.week_6.visualize_results import ResultsVisualizer


def test_minimal_workflow():
    """Test the complete workflow with minimal settings."""
    print("🧪 Testing CSV + RLiable Workflow")
    print("=" * 40)
    
    # Create temporary directory for test
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir) / "test_experiment"
        
        print(f"Test directory: {test_dir}")
        
        # Step 1: Run minimal experiments
        print("\n1. Running minimal experiments...")
        runner = ExperimentRunner(
            env_name="LunarLander-v3",
            output_dir=str(test_dir)
        )
        
        try:
            runner.run_comparison(
                algorithms=["value", "gae"],  # Only 2 algorithms
                seeds=[0, 1],  # Only 2 seeds
                total_steps=10000,  # Very short training
                eval_interval=5000,  # Only 2 evaluation points
                eval_episodes=2,  # Minimal evaluation
                lr_actor=5e-3,
                lr_critic=1e-2
            )
            print("✅ Experiments completed successfully")
        except Exception as e:
            print(f"❌ Experiment failed: {e}")
            return False
        
        # Step 2: Check CSV files were created
        print("\n2. Checking CSV files...")
        eval_file = test_dir / "evaluation_results.csv"
        final_file = test_dir / "final_results.csv"
        config_file = test_dir / "experiment_config.txt"
        
        if eval_file.exists():
            print(f"✅ Evaluation results file created: {eval_file}")
        else:
            print(f"❌ Missing evaluation results file: {eval_file}")
            return False
        
        if final_file.exists():
            print(f"✅ Final results file created: {final_file}")
        else:
            print(f"❌ Missing final results file: {final_file}")
            return False
        
        if config_file.exists():
            print(f"✅ Config file created: {config_file}")
        else:
            print(f"❌ Missing config file: {config_file}")
            return False
        
        # Step 3: Test visualization
        print("\n3. Testing visualization...")
        try:
            visualizer = ResultsVisualizer(str(test_dir))
            
            # Check data loading
            if visualizer.eval_df is not None:
                print(f"✅ Evaluation data loaded: {len(visualizer.eval_df)} rows")
            else:
                print("❌ Failed to load evaluation data")
                return False
            
            if visualizer.final_df is not None:
                print(f"✅ Final results loaded: {len(visualizer.final_df)} rows")
            else:
                print("❌ Failed to load final results")
                return False
            
            # Test summary
            print("\n4. Testing summary...")
            visualizer.print_summary()
            
            # Test plot creation (without showing)
            print("\n5. Testing plot creation...")
            visualizer.create_all_plots(save_plots=True, show_plots=False)
            
            # Check if plots were created
            plots_dir = test_dir / "plots"
            if plots_dir.exists():
                plot_files = list(plots_dir.glob("*.png"))
                print(f"✅ Created {len(plot_files)} plot files:")
                for plot_file in plot_files:
                    print(f"   - {plot_file.name}")
            else:
                print("❌ No plots directory created")
                return False
            
            print("✅ Visualization completed successfully")
            
        except Exception as e:
            print(f"❌ Visualization failed: {e}")
            return False
        
        print("\n" + "="*40)
        print("🎉 ALL TESTS PASSED!")
        print("="*40)
        print("\nThe CSV + RLiable workflow is working correctly.")
        print("You can now run full experiments with confidence.")
        
        return True


def test_csv_format():
    """Test CSV format compatibility."""
    print("\n📄 Testing CSV Format")
    print("-" * 25)
    
    import pandas as pd
    import numpy as np
    
    # Create sample data
    eval_data = {
        'algorithm': ['value', 'value', 'gae', 'gae'],
        'seed': [0, 1, 0, 1],
        'step': [5000, 5000, 5000, 5000],
        'return': [-150.2, -142.8, -135.4, -128.1],
        'return_std': [45.3, 52.1, 41.2, 38.9],
        'eval_time': [2.1, 2.2, 2.0, 2.1],
        'training_time': [45.2, 46.1, 44.8, 45.5]
    }
    
    final_data = {
        'algorithm': ['value', 'value', 'gae', 'gae'],
        'seed': [0, 1, 0, 1],
        'total_steps': [10000, 10000, 10000, 10000],
        'final_return': [180.5, 220.3, 210.2, 235.8],
        'final_return_std': [25.2, 18.9, 22.1, 19.4],
        'training_time': [245.7, 251.2, 248.3, 252.1],
        'success_rate': [0.0, 1.0, 1.0, 1.0]
    }
    
    # Test DataFrame creation
    try:
        eval_df = pd.DataFrame(eval_data)
        final_df = pd.DataFrame(final_data)
        print("✅ DataFrames created successfully")
        
        # Test RLiable format conversion
        score_dict = {}
        for algorithm in final_df['algorithm'].unique():
            alg_data = final_df[final_df['algorithm'] == algorithm]
            score_dict[algorithm.upper()] = alg_data['final_return'].values
        
        print(f"✅ RLiable format: {score_dict}")
        
        # Test basic statistics
        for alg, scores in score_dict.items():
            mean_score = np.mean(scores)
            std_score = np.std(scores)
            print(f"   {alg}: {mean_score:.1f} ± {std_score:.1f}")
        
        print("✅ CSV format is compatible")
        return True
        
    except Exception as e:
        print(f"❌ CSV format test failed: {e}")
        return False


def main():
    """Main test function."""
    print("Actor-Critic Workflow Test Suite")
    print("=" * 35)
    print()
    print("This test verifies that the CSV + RLiable workflow")
    print("works correctly with minimal computational requirements.")
    print()
    
    # Test CSV format
    if not test_csv_format():
        print("❌ CSV format test failed")
        return
    
    # Test full workflow
    if not test_minimal_workflow():
        print("❌ Workflow test failed")
        return
    
    print("\n" + "="*50)
    print("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
    print("="*50)
    print()
    print("The workflow is ready for use. Next steps:")
    print("1. Run demo_workflow.py for a complete demonstration")
    print("2. Use run_experiments.py for your own experiments")
    print("3. Use visualize_results.py to analyze results")
    print()
    print("Example commands:")
    print("  python demo_workflow.py")
    print("  python run_experiments.py --algorithms value gae --seeds 0 1 2")
    print("  python visualize_results.py outputs/experiments/TIMESTAMP")


if __name__ == "__main__":
    main()
