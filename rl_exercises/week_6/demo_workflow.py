#!/usr/bin/env python3
"""
Demo workflow for Actor-Critic baseline comparison.

This script demonstrates the complete workflow:
1. Run experiments and save to CSV
2. Load CSV data and create visualizations with RLiable
"""

import sys
import subprocess
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from rl_exercises.week_6.run_experiments import ExperimentRunner
from rl_exercises.week_6.visualize_results import ResultsVisualizer


def demo_quick_experiment():
    """Run a quick demo experiment."""
    print("🚀 Demo: Quick Baseline Comparison")
    print("=" * 50)
    print("This demo runs a minimal experiment for testing:")
    print("- 2 seeds per algorithm")
    print("- 4 algorithms: none, avg, value, gae")
    print("- 20,000 steps per run")
    print("- Total: 8 experiments (~10 minutes)")
    print()
    
    # Create experiment runner
    runner = ExperimentRunner(
        env_name="LunarLander-v3",
        output_dir="outputs/demo_experiment"
    )
    
    # Run quick comparison
    runner.run_comparison(
        algorithms=["none", "avg", "value", "gae"],
        seeds=[0, 1],  # Only 2 seeds for demo
        total_steps=20000,  # Reduced steps
        eval_interval=5000,
        eval_episodes=3,
        # Agent hyperparameters
        lr_actor=5e-3,
        lr_critic=1e-2,
        gamma=0.99,
        gae_lambda=0.95,
        hidden_size=128
    )
    
    print(f"\n✅ Experiments completed! Results saved to: {runner.output_dir}")
    return runner.output_dir


def demo_visualization(results_dir):
    """Create visualizations from experiment results."""
    print("\n📊 Demo: Creating Visualizations")
    print("=" * 40)
    
    # Create visualizer
    visualizer = ResultsVisualizer(results_dir)
    
    # Print summary
    visualizer.print_summary()
    
    # Create all plots
    visualizer.create_all_plots(save_plots=True, show_plots=False)
    
    print(f"\n✅ Visualizations completed! Plots saved to: {visualizer.output_dir}")


def demo_command_line():
    """Demonstrate command line usage."""
    print("\n💻 Demo: Command Line Usage")
    print("=" * 40)
    print()
    print("You can also use the scripts from command line:")
    print()
    print("1. Run experiments:")
    print("   python run_experiments.py --algorithms none value gae --seeds 0 1 2 --steps 50000")
    print()
    print("2. Create visualizations:")
    print("   python visualize_results.py outputs/experiments/TIMESTAMP --show-plots")
    print()
    print("3. Quick demo:")
    print("   python run_experiments.py --algorithms value gae --seeds 0 1 --steps 20000")
    print("   python visualize_results.py outputs/experiments/TIMESTAMP")
    print()


def demo_csv_format():
    """Show the CSV format used."""
    print("\n📄 Demo: CSV Data Format")
    print("=" * 30)
    print()
    print("The experiments save data in two CSV files:")
    print()
    print("1. evaluation_results.csv (for learning curves):")
    print("   Columns: algorithm, seed, step, return, return_std, eval_time, training_time")
    print("   Example:")
    print("   algorithm,seed,step,return,return_std,eval_time,training_time")
    print("   value,0,5000,-150.2,45.3,2.1,45.2")
    print("   value,0,10000,-89.7,38.1,2.0,92.1")
    print("   ...")
    print()
    print("2. final_results.csv (for aggregate analysis):")
    print("   Columns: algorithm, seed, total_steps, final_return, final_return_std, training_time, success_rate")
    print("   Example:")
    print("   algorithm,seed,total_steps,final_return,final_return_std,training_time,success_rate")
    print("   value,0,50000,180.5,25.2,245.7,0.0")
    print("   value,1,50000,220.3,18.9,251.2,1.0")
    print("   ...")
    print()
    print("This format is compatible with:")
    print("- RLiable for statistical analysis")
    print("- Pandas for data manipulation")
    print("- Standard plotting libraries")
    print("- Easy sharing and archiving")


def demo_rliable_integration():
    """Explain RLiable integration."""
    print("\n📈 Demo: RLiable Integration")
    print("=" * 35)
    print()
    print("RLiable provides robust statistical analysis for RL:")
    print()
    print("Features included:")
    print("- Confidence intervals for aggregate metrics")
    print("- Performance profiles across score thresholds")
    print("- Sample efficiency analysis")
    print("- Probability of improvement comparisons")
    print("- Bootstrap-based statistical tests")
    print()
    print("Data format for RLiable:")
    print("- Dictionary: {algorithm_name: np.array([seed_scores])}")
    print("- Example: {'VALUE': [180.5, 220.3, 195.1], 'GAE': [210.2, 235.8, 198.9]}")
    print()
    print("The visualize_results.py script automatically:")
    print("1. Loads CSV data")
    print("2. Converts to RLiable format")
    print("3. Creates statistical plots")
    print("4. Saves publication-quality figures")


def main():
    """Main demo function."""
    print("Actor-Critic Baseline Comparison Demo")
    print("=" * 40)
    print()
    print("This demo shows the complete workflow for comparing")
    print("Actor-Critic baseline strategies with statistical analysis.")
    print()
    
    print("Demo options:")
    print("1. Run quick experiment + visualization")
    print("2. Show CSV data format")
    print("3. Show RLiable integration")
    print("4. Show command line usage")
    print("5. All of the above")
    
    choice = input("\nEnter your choice (1-5): ").strip()
    
    if choice == "1" or choice == "5":
        # Run experiment and visualization
        results_dir = demo_quick_experiment()
        demo_visualization(results_dir)
    
    if choice == "2" or choice == "5":
        demo_csv_format()
    
    if choice == "3" or choice == "5":
        demo_rliable_integration()
    
    if choice == "4" or choice == "5":
        demo_command_line()
    
    if choice not in ["1", "2", "3", "4", "5"]:
        print("Invalid choice. Please run again.")
        return
    
    print("\n" + "="*60)
    print("DEMO COMPLETED")
    print("="*60)
    print()
    print("Next steps:")
    print("1. Run your own experiments with different parameters")
    print("2. Try the command line interface")
    print("3. Analyze your results with RLiable")
    print("4. Share your CSV data for reproducible research")
    print()
    print("Files created:")
    print("- run_experiments.py: Run experiments and save to CSV")
    print("- visualize_results.py: Load CSV and create plots")
    print("- demo_workflow.py: This demo script")


if __name__ == "__main__":
    main()
