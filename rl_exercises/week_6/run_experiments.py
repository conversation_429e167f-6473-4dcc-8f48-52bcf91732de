import sys
import time
import csv
from pathlib import Path
from datetime import datetime
import gymnasium as gym
import numpy as np

from rl_exercises.week_6.actor_critic import ActorCriticAgent, set_seed

# Configuration
algorithms = ["none", "avg", "value", "gae"]
seeds = [0, 1, 2]
total_steps = 50000
eval_interval = 5000
eval_episodes = 5

# Create output directory
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
output_dir = Path("outputs") / "experiments" / timestamp
output_dir.mkdir(parents=True, exist_ok=True)

eval_results_file = output_dir / "evaluation_results.csv"
final_results_file = output_dir / "final_results.csv"

print(f"Running baseline comparison...")
print(f"Algorithms: {algorithms}")
print(f"Seeds: {seeds}")
print(f"Output: {output_dir}")

all_eval_data = []
all_final_data = []

for algorithm in algorithms:
    print(f"\n--- Training {algorithm.upper()} ---")
    
    for seed in seeds:
        print(f"Running {algorithm} with seed {seed}...")
        
        # Create environment and agent
        env = gym.make("LunarLander-v3")
        set_seed(env, seed)
        agent = ActorCriticAgent(env, baseline_type=algorithm, seed=seed)
        
        # Training loop
        eval_env = gym.make("LunarLander-v3")
        step_count = 0
        start_time = time.time()
        
        while step_count < total_steps:
            state, _ = env.reset()
            done = False
            trajectory = []
            
            while not done and step_count < total_steps:
                action, logp = agent.predict_action(state)
                next_state, reward, term, trunc, _ = env.step(action)
                done = term or trunc
                trajectory.append((state, action, float(reward), next_state, done, logp))
                state = next_state
                step_count += 1
                
                # Evaluation
                if step_count % eval_interval == 0:
                    mean_return, std_return = agent.evaluate(eval_env, num_episodes=eval_episodes)
                    
                    all_eval_data.append({
                        'algorithm': algorithm,
                        'seed': seed,
                        'step': step_count,
                        'return': mean_return,
                        'return_std': std_return,
                        'eval_time': 0.0,
                        'training_time': time.time() - start_time
                    })
                    
                    print(f"  Step {step_count:6d} | Return {mean_return:6.1f} ± {std_return:4.1f}")
            
            agent.update_agent(trajectory)
        
        # Final evaluation
        final_mean, final_std = agent.evaluate(eval_env, num_episodes=20)
        training_time = time.time() - start_time
        
        all_final_data.append({
            'algorithm': algorithm,
            'seed': seed,
            'total_steps': total_steps,
            'final_return': final_mean,
            'final_return_std': final_std,
            'training_time': training_time,
            'success_rate': 1.0 if final_mean > 200 else 0.0
        })
        
        print(f"  Completed | Final: {final_mean:.1f} ± {final_std:.1f}")
        
        env.close()
        eval_env.close()

# Save results to CSV
with open(eval_results_file, 'w', newline='') as f:
    writer = csv.DictWriter(f, fieldnames=['algorithm', 'seed', 'step', 'return', 'return_std', 'eval_time', 'training_time'])
    writer.writeheader()
    writer.writerows(all_eval_data)

with open(final_results_file, 'w', newline='') as f:
    writer = csv.DictWriter(f, fieldnames=['algorithm', 'seed', 'total_steps', 'final_return', 'final_return_std', 'training_time', 'success_rate'])
    writer.writeheader()
    writer.writerows(all_final_data)

# Print summary
print("\n" + "="*50)
print("RESULTS SUMMARY")
print("="*50)

algorithm_results = {}
for result in all_final_data:
    alg = result['algorithm']
    if alg not in algorithm_results:
        algorithm_results[alg] = []
    algorithm_results[alg].append(result['final_return'])

for rank, (alg, returns) in enumerate(sorted(algorithm_results.items(), key=lambda x: np.mean(x[1]), reverse=True), 1):
    mean_return = np.mean(returns)
    std_return = np.std(returns)
    success_rate = np.mean(np.array(returns) > 200)
    print(f"{rank}. {alg.upper():8s}: {mean_return:6.1f} ± {std_return:4.1f} (success: {success_rate:.1%})")

print(f"\nResults saved to: {output_dir}")
print(f"  - {eval_results_file}")
print(f"  - {final_results_file}")
