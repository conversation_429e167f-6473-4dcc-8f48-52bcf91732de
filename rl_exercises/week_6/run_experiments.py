#!/usr/bin/env python3
"""
Experiment runner for Actor-Critic baseline comparison.

This script runs experiments across multiple seeds and baselines,
saving results to CSV files compatible with RLiable analysis.

CSV Format:
- One row per evaluation point
- Columns: algorithm, seed, step, return, episode_length, training_time
"""

import os
import sys
import time
import csv
import argparse
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime

import gymnasium as gym
import numpy as np

# Add the project root to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from rl_exercises.week_6.actor_critic import ActorCriticAgent, set_seed


class ExperimentRunner:
    """
    Runs Actor-Critic experiments and saves results to CSV.
    """
    
    def __init__(self, 
                 env_name: str = "LunarLander-v3",
                 output_dir: str = None):
        """
        Initialize the experiment runner.
        
        Parameters
        ----------
        env_name : str
            Environment name
        output_dir : str
            Output directory for results
        """
        self.env_name = env_name
        
        # Create output directory
        if output_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.output_dir = Path("outputs") / "experiments" / timestamp
        else:
            self.output_dir = Path(output_dir)
        
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # CSV file paths
        self.eval_results_file = self.output_dir / "evaluation_results.csv"
        self.final_results_file = self.output_dir / "final_results.csv"
        self.config_file = self.output_dir / "experiment_config.txt"
        
        print(f"Experiment runner initialized")
        print(f"Environment: {self.env_name}")
        print(f"Output directory: {self.output_dir}")
    
    def run_single_experiment(self,
                             algorithm: str,
                             seed: int,
                             total_steps: int = 100000,
                             eval_interval: int = 5000,
                             eval_episodes: int = 5,
                             **agent_kwargs) -> Dict[str, Any]:
        """
        Run a single experiment with given parameters.
        
        Parameters
        ----------
        algorithm : str
            Baseline algorithm name
        seed : int
            Random seed
        total_steps : int
            Total training steps
        eval_interval : int
            Evaluation interval
        eval_episodes : int
            Number of evaluation episodes
        **agent_kwargs
            Additional arguments for agent creation
            
        Returns
        -------
        results : dict
            Experiment results
        """
        print(f"Running {algorithm} with seed {seed}...")
        
        # Create environment
        env = gym.make(self.env_name)
        set_seed(env, seed)
        
        # Create agent
        agent = ActorCriticAgent(
            env,
            baseline_type=algorithm,
            seed=seed,
            **agent_kwargs
        )
        
        # Training loop with evaluation tracking
        eval_env = gym.make(self.env_name)
        step_count = 0
        eval_data = []
        
        start_time = time.time()
        
        while step_count < total_steps:
            state, _ = env.reset()
            done = False
            trajectory = []
            
            while not done and step_count < total_steps:
                action, logp = agent.predict_action(state)
                next_state, reward, term, trunc, _ = env.step(action)
                done = term or trunc
                trajectory.append((state, action, float(reward), next_state, done, logp))
                state = next_state
                step_count += 1
                
                # Evaluation
                if step_count % eval_interval == 0:
                    eval_start = time.time()
                    mean_return, std_return = agent.evaluate(eval_env, num_episodes=eval_episodes)
                    eval_time = time.time() - eval_start
                    
                    # Store evaluation data
                    eval_data.append({
                        'algorithm': algorithm,
                        'seed': seed,
                        'step': step_count,
                        'return': mean_return,
                        'return_std': std_return,
                        'eval_time': eval_time,
                        'training_time': time.time() - start_time
                    })
                    
                    print(f"  {algorithm}-{seed}: Step {step_count:6d} | "
                          f"Return {mean_return:6.1f} ± {std_return:4.1f}")
            
            # Update agent
            agent.update_agent(trajectory)
        
        total_training_time = time.time() - start_time
        
        # Final evaluation
        final_mean, final_std = agent.evaluate(eval_env, num_episodes=20)
        
        env.close()
        eval_env.close()
        
        # Final results
        final_result = {
            'algorithm': algorithm,
            'seed': seed,
            'total_steps': total_steps,
            'final_return': final_mean,
            'final_return_std': final_std,
            'training_time': total_training_time,
            'success_rate': 1.0 if final_mean > 200 else 0.0
        }
        
        print(f"  {algorithm}-{seed}: Completed | Final: {final_mean:.1f} ± {final_std:.1f}")
        
        return {
            'eval_data': eval_data,
            'final_result': final_result
        }
    
    def save_to_csv(self, eval_data: List[Dict], final_data: List[Dict]):
        """
        Save results to CSV files.
        
        Parameters
        ----------
        eval_data : List[Dict]
            Evaluation data from all experiments
        final_data : List[Dict]
            Final results from all experiments
        """
        # Save evaluation results (for learning curves)
        eval_fieldnames = ['algorithm', 'seed', 'step', 'return', 'return_std', 'eval_time', 'training_time']
        
        with open(self.eval_results_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=eval_fieldnames)
            writer.writeheader()
            writer.writerows(eval_data)
        
        # Save final results (for aggregate analysis)
        final_fieldnames = ['algorithm', 'seed', 'total_steps', 'final_return', 'final_return_std', 
                           'training_time', 'success_rate']
        
        with open(self.final_results_file, 'w', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=final_fieldnames)
            writer.writeheader()
            writer.writerows(final_data)
        
        print(f"Results saved to:")
        print(f"  Evaluation data: {self.eval_results_file}")
        print(f"  Final results: {self.final_results_file}")
    
    def save_config(self, config: Dict[str, Any]):
        """
        Save experiment configuration.
        
        Parameters
        ----------
        config : dict
            Experiment configuration
        """
        with open(self.config_file, 'w') as f:
            f.write("Experiment Configuration\n")
            f.write("=" * 30 + "\n\n")
            for key, value in config.items():
                f.write(f"{key}: {value}\n")
        
        print(f"Configuration saved to: {self.config_file}")
    
    def run_comparison(self,
                      algorithms: List[str] = None,
                      seeds: List[int] = None,
                      total_steps: int = 100000,
                      eval_interval: int = 5000,
                      eval_episodes: int = 5,
                      **agent_kwargs) -> None:
        """
        Run complete baseline comparison.
        
        Parameters
        ----------
        algorithms : List[str]
            List of baseline algorithms to compare
        seeds : List[int]
            List of random seeds
        total_steps : int
            Total training steps per experiment
        eval_interval : int
            Evaluation interval
        eval_episodes : int
            Number of evaluation episodes
        **agent_kwargs
            Additional arguments for agent creation
        """
        if algorithms is None:
            algorithms = ["none", "avg", "value", "gae"]
        if seeds is None:
            seeds = [0, 1, 2, 3, 4]
        
        # Save configuration
        config = {
            'env_name': self.env_name,
            'algorithms': algorithms,
            'seeds': seeds,
            'total_steps': total_steps,
            'eval_interval': eval_interval,
            'eval_episodes': eval_episodes,
            'agent_kwargs': agent_kwargs,
            'timestamp': datetime.now().isoformat()
        }
        self.save_config(config)
        
        print(f"\nStarting baseline comparison...")
        print(f"Algorithms: {algorithms}")
        print(f"Seeds: {seeds}")
        print(f"Total experiments: {len(algorithms)} × {len(seeds)} = {len(algorithms) * len(seeds)}")
        print(f"Estimated time: ~{len(algorithms) * len(seeds) * total_steps / 10000:.0f} minutes")
        print()
        
        all_eval_data = []
        all_final_data = []
        
        for algorithm in algorithms:
            print(f"\n--- Training {algorithm.upper()} baseline ---")
            
            for seed in seeds:
                try:
                    results = self.run_single_experiment(
                        algorithm=algorithm,
                        seed=seed,
                        total_steps=total_steps,
                        eval_interval=eval_interval,
                        eval_episodes=eval_episodes,
                        **agent_kwargs
                    )
                    
                    all_eval_data.extend(results['eval_data'])
                    all_final_data.append(results['final_result'])
                    
                except Exception as e:
                    print(f"Error in {algorithm}-{seed}: {e}")
                    continue
        
        # Save all results
        self.save_to_csv(all_eval_data, all_final_data)
        
        # Print summary
        self.print_summary(all_final_data)
        
        print(f"\n✅ Comparison completed! Results saved to: {self.output_dir}")
    
    def print_summary(self, final_data: List[Dict]):
        """Print a summary of results."""
        print("\n" + "="*60)
        print("EXPERIMENT SUMMARY")
        print("="*60)
        
        # Group by algorithm
        algorithm_results = {}
        for result in final_data:
            alg = result['algorithm']
            if alg not in algorithm_results:
                algorithm_results[alg] = []
            algorithm_results[alg].append(result['final_return'])
        
        # Calculate statistics and rankings
        algorithm_stats = {}
        for alg, returns in algorithm_results.items():
            algorithm_stats[alg] = {
                'mean': np.mean(returns),
                'std': np.std(returns),
                'min': np.min(returns),
                'max': np.max(returns),
                'success_rate': np.mean(np.array(returns) > 200)
            }
        
        # Sort by mean performance
        sorted_algorithms = sorted(algorithm_stats.items(), 
                                 key=lambda x: x[1]['mean'], reverse=True)
        
        print("PERFORMANCE RANKING:")
        print("-" * 40)
        for rank, (alg, stats) in enumerate(sorted_algorithms, 1):
            print(f"{rank}. {alg.upper():8s}: {stats['mean']:6.1f} ± {stats['std']:4.1f} "
                  f"(success: {stats['success_rate']:.1%})")
        
        print("="*60)


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description="Run Actor-Critic baseline comparison experiments")
    
    parser.add_argument("--algorithms", nargs="+", default=["none", "avg", "value", "gae"],
                       help="Baseline algorithms to compare")
    parser.add_argument("--seeds", nargs="+", type=int, default=[0, 1, 2, 3, 4],
                       help="Random seeds to use")
    parser.add_argument("--steps", type=int, default=100000,
                       help="Total training steps per experiment")
    parser.add_argument("--eval-interval", type=int, default=5000,
                       help="Evaluation interval")
    parser.add_argument("--eval-episodes", type=int, default=5,
                       help="Number of evaluation episodes")
    parser.add_argument("--output-dir", type=str, default=None,
                       help="Output directory")
    parser.add_argument("--env", type=str, default="LunarLander-v3",
                       help="Environment name")
    
    # Agent hyperparameters
    parser.add_argument("--lr-actor", type=float, default=5e-3,
                       help="Actor learning rate")
    parser.add_argument("--lr-critic", type=float, default=1e-2,
                       help="Critic learning rate")
    parser.add_argument("--gamma", type=float, default=0.99,
                       help="Discount factor")
    parser.add_argument("--gae-lambda", type=float, default=0.95,
                       help="GAE lambda parameter")
    parser.add_argument("--hidden-size", type=int, default=128,
                       help="Hidden layer size")
    
    args = parser.parse_args()
    
    # Create runner
    runner = ExperimentRunner(env_name=args.env, output_dir=args.output_dir)
    
    # Agent kwargs
    agent_kwargs = {
        'lr_actor': args.lr_actor,
        'lr_critic': args.lr_critic,
        'gamma': args.gamma,
        'gae_lambda': args.gae_lambda,
        'hidden_size': args.hidden_size
    }
    
    # Run comparison
    runner.run_comparison(
        algorithms=args.algorithms,
        seeds=args.seeds,
        total_steps=args.steps,
        eval_interval=args.eval_interval,
        eval_episodes=args.eval_episodes,
        **agent_kwargs
    )


if __name__ == "__main__":
    main()
