# Actor-Critic Baseline Comparison: CSV + RLiable Workflow

This workflow separates experiment execution from visualization, using CSV files as the data exchange format. This approach provides better modularity, reproducibility, and compatibility with RLiable.

## 🏗️ Architecture

```
┌─────────────────┐    CSV Files    ┌──────────────────┐
│  run_experiments │ ──────────────► │ visualize_results │
│      .py         │                 │       .py        │
└─────────────────┘                 └──────────────────┘
        │                                      │
        ▼                                      ▼
┌─────────────────┐                 ┌──────────────────┐
│ evaluation_     │                 │ Publication-     │
│ results.csv     │                 │ quality plots    │
│ final_results.  │                 │ with RLiable     │
│ csv             │                 │                  │
└─────────────────┘                 └──────────────────┘
```

## 📁 Files Overview

### Core Scripts
- **`run_experiments.py`**: Runs experiments and saves to CSV
- **`visualize_results.py`**: Loads CSV and creates RLiable plots
- **`demo_workflow.py`**: Complete workflow demonstration

### Data Files (Generated)
- **`evaluation_results.csv`**: Learning curves data
- **`final_results.csv`**: Final performance data
- **`experiment_config.txt`**: Experiment configuration

## 🚀 Quick Start

### Option 1: Demo Workflow
```bash
cd rl_exercises/week_6
python demo_workflow.py
# Choose option 1 for complete demo
```

### Option 2: Step-by-Step
```bash
# Step 1: Run experiments
python run_experiments.py --algorithms none avg value gae --seeds 0 1 2 --steps 50000

# Step 2: Visualize results
python visualize_results.py outputs/experiments/TIMESTAMP
```

### Option 3: Quick Test
```bash
# Quick test with minimal training
python run_experiments.py --algorithms value gae --seeds 0 1 --steps 20000
python visualize_results.py outputs/experiments/TIMESTAMP --show-plots
```

## 📊 CSV Data Format

### evaluation_results.csv
Learning curves data for plotting training progress:

| Column | Type | Description |
|--------|------|-------------|
| algorithm | str | Baseline algorithm (none/avg/value/gae) |
| seed | int | Random seed |
| step | int | Training step |
| return | float | Average evaluation return |
| return_std | float | Standard deviation of returns |
| eval_time | float | Time for evaluation |
| training_time | float | Cumulative training time |

**Example:**
```csv
algorithm,seed,step,return,return_std,eval_time,training_time
value,0,5000,-150.2,45.3,2.1,45.2
value,0,10000,-89.7,38.1,2.0,92.1
value,1,5000,-142.8,52.1,2.2,46.1
gae,0,5000,-135.4,41.2,2.0,44.8
```

### final_results.csv
Final performance data for aggregate analysis:

| Column | Type | Description |
|--------|------|-------------|
| algorithm | str | Baseline algorithm |
| seed | int | Random seed |
| total_steps | int | Total training steps |
| final_return | float | Final evaluation return |
| final_return_std | float | Standard deviation |
| training_time | float | Total training time |
| success_rate | float | Success indicator (1.0 if return > 200) |

**Example:**
```csv
algorithm,seed,total_steps,final_return,final_return_std,training_time,success_rate
value,0,50000,180.5,25.2,245.7,0.0
value,1,50000,220.3,18.9,251.2,1.0
gae,0,50000,210.2,22.1,248.3,1.0
```

## 🔧 Command Line Usage

### run_experiments.py

```bash
# Basic usage
python run_experiments.py

# Custom configuration
python run_experiments.py \
    --algorithms none avg value gae \
    --seeds 0 1 2 3 4 \
    --steps 100000 \
    --eval-interval 5000 \
    --eval-episodes 5 \
    --output-dir my_experiment

# Hyperparameter tuning
python run_experiments.py \
    --lr-actor 1e-3 \
    --lr-critic 5e-3 \
    --gamma 0.99 \
    --gae-lambda 0.95 \
    --hidden-size 256
```

### visualize_results.py

```bash
# Basic usage
python visualize_results.py outputs/experiments/TIMESTAMP

# Show plots interactively
python visualize_results.py outputs/experiments/TIMESTAMP --show-plots

# Don't save plots to files
python visualize_results.py outputs/experiments/TIMESTAMP --no-save --show-plots
```

## 📈 Generated Visualizations

### 1. Learning Curves
- **File**: `learning_curves.png`
- **Content**: Training progress with confidence intervals
- **Features**: Mean ± CI, success threshold line

### 2. RLiable Analysis
- **File**: `rliable_analysis.png`
- **Content**: Comprehensive statistical analysis
- **Features**: 
  - Aggregate metrics with bootstrap CIs
  - Performance profiles
  - Sample efficiency curves
  - Probability of improvement

### 3. Summary Statistics
- **File**: `summary_statistics.png`
- **Content**: Box plots, success rates, training times
- **Features**: Distribution analysis, performance vs efficiency

## 🔬 RLiable Integration

### Data Conversion
The CSV data is automatically converted to RLiable format:

```python
# CSV data
df = pd.read_csv("final_results.csv")

# Convert to RLiable format
score_dict = {}
for algorithm in df['algorithm'].unique():
    alg_data = df[df['algorithm'] == algorithm]
    score_dict[algorithm.upper()] = alg_data['final_return'].values

# Result: {'VALUE': [180.5, 220.3, 195.1], 'GAE': [210.2, 235.8, 198.9]}
```

### Statistical Analysis
RLiable provides robust statistical methods:

- **Bootstrap confidence intervals** (50,000 bootstrap samples)
- **Aggregate metrics**: median, mean, optimality gap
- **Performance profiles**: fraction of runs above thresholds
- **Sample efficiency**: steps to reach performance levels

## 🎯 Workflow Benefits

### 1. Modularity
- **Separate concerns**: Experiments vs visualization
- **Independent execution**: Run experiments once, visualize many times
- **Easy debugging**: Isolate issues in specific components

### 2. Reproducibility
- **Standardized format**: CSV files are universal
- **Version control**: Track data and code separately
- **Sharing**: Easy to share results without code dependencies

### 3. Flexibility
- **Multiple visualizations**: Create different plots from same data
- **External analysis**: Use data in other tools (R, MATLAB, etc.)
- **Incremental experiments**: Add new seeds/algorithms easily

### 4. Performance
- **Parallel experiments**: Run multiple seeds simultaneously
- **Cached results**: No need to re-run experiments for new plots
- **Efficient storage**: CSV is compact and fast to load

## 🛠️ Advanced Usage

### Custom Analysis
```python
import pandas as pd
from visualize_results import ResultsVisualizer

# Load your data
visualizer = ResultsVisualizer("path/to/results")

# Access raw data
eval_df = visualizer.eval_df
final_df = visualizer.final_df

# Custom analysis
best_algorithm = final_df.groupby('algorithm')['final_return'].mean().idxmax()
print(f"Best algorithm: {best_algorithm}")

# Custom plots
visualizer.plot_learning_curves(show_plot=True)
```

### Combining Multiple Experiments
```python
import pandas as pd

# Load multiple experiments
exp1 = pd.read_csv("experiment1/final_results.csv")
exp2 = pd.read_csv("experiment2/final_results.csv")

# Add experiment identifier
exp1['experiment'] = 'exp1'
exp2['experiment'] = 'exp2'

# Combine and analyze
combined = pd.concat([exp1, exp2])
```

### Export for Publications
```python
# High-resolution plots
visualizer.create_all_plots(save_plots=True, show_plots=False)

# Custom DPI and format
import matplotlib.pyplot as plt
plt.savefig("figure.pdf", dpi=300, bbox_inches='tight', format='pdf')
```

## 📋 Requirements

### Essential
```bash
pip install gymnasium[box2d]
pip install torch
pip install pandas
pip install matplotlib
pip install seaborn
pip install numpy
pip install scipy
```

### Optional (for RLiable)
```bash
pip install rliable
```

## 🔍 Troubleshooting

### Common Issues

1. **Missing CSV files**
   - Ensure experiments completed successfully
   - Check output directory path

2. **RLiable import errors**
   - Install with `pip install rliable`
   - Visualization works without RLiable (reduced functionality)

3. **Memory issues with large experiments**
   - Process results in chunks
   - Use fewer seeds for testing

4. **Inconsistent results**
   - Use more seeds (≥5 recommended)
   - Check random seed consistency

### Performance Tips

- **Quick testing**: 2-3 seeds, 20k-50k steps
- **Reliable results**: 5+ seeds, 100k+ steps
- **Publication quality**: 10+ seeds, 200k+ steps

## 📚 References

- **RLiable**: Agarwal et al. "Deep Reinforcement Learning at the Edge of the Statistical Precipice" (NeurIPS 2021)
- **CSV Format**: RFC 4180 standard
- **Statistical Methods**: Bootstrap confidence intervals, performance profiles

## 🤝 Contributing

To extend the workflow:

1. **Add new metrics**: Modify CSV columns in `run_experiments.py`
2. **New visualizations**: Add methods to `visualize_results.py`
3. **Different environments**: Change `env_name` parameter
4. **New algorithms**: Extend baseline strategies

Happy experimenting! 🚀
