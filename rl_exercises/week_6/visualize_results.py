#!/usr/bin/env python3
"""
Visualization tool for Actor-Critic baseline comparison results.

This script loads CSV results from experiments and creates publication-quality
plots using RLiable for robust statistical analysis.

Expected CSV format:
- evaluation_results.csv: algorithm, seed, step, return, return_std, eval_time, training_time
- final_results.csv: algorithm, seed, total_steps, final_return, final_return_std, training_time, success_rate
"""

import sys
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# RLiable imports
try:
    from rliable import library as rly
    from rliable import metrics
    from rliable import plot_utils
    RLIABLE_AVAILABLE = True
except ImportError:
    print("Warning: RLiable not available. Install with: pip install rliable")
    RLIABLE_AVAILABLE = False

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


class ResultsVisualizer:
    """
    Visualizes Actor-Critic baseline comparison results from CSV files.
    """
    
    def __init__(self, results_dir: str):
        """
        Initialize the visualizer.
        
        Parameters
        ----------
        results_dir : str
            Directory containing CSV result files
        """
        self.results_dir = Path(results_dir)
        self.eval_file = self.results_dir / "evaluation_results.csv"
        self.final_file = self.results_dir / "final_results.csv"
        
        # Load data
        self.eval_df = None
        self.final_df = None
        self.load_data()
        
        # Output directory for plots
        self.output_dir = self.results_dir / "plots"
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"Results visualizer initialized")
        print(f"Results directory: {self.results_dir}")
        print(f"Output directory: {self.output_dir}")
    
    def load_data(self):
        """Load CSV data files."""
        try:
            if self.eval_file.exists():
                self.eval_df = pd.read_csv(self.eval_file)
                print(f"Loaded evaluation data: {len(self.eval_df)} rows")
            else:
                print(f"Warning: {self.eval_file} not found")
            
            if self.final_file.exists():
                self.final_df = pd.read_csv(self.final_file)
                print(f"Loaded final results: {len(self.final_df)} rows")
            else:
                print(f"Warning: {self.final_file} not found")
                
        except Exception as e:
            print(f"Error loading data: {e}")
    
    def plot_learning_curves(self, save_plot: bool = True, show_plot: bool = False) -> None:
        """
        Plot learning curves with confidence intervals.
        
        Parameters
        ----------
        save_plot : bool
            Whether to save the plot
        show_plot : bool
            Whether to display the plot
        """
        if self.eval_df is None:
            print("No evaluation data available")
            return
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        algorithms = self.eval_df['algorithm'].unique()
        colors = plt.cm.Set1(np.linspace(0, 1, len(algorithms)))
        
        for i, algorithm in enumerate(algorithms):
            alg_data = self.eval_df[self.eval_df['algorithm'] == algorithm]
            
            # Group by step and calculate statistics
            grouped = alg_data.groupby('step')['return'].agg(['mean', 'std', 'count']).reset_index()
            
            # Calculate confidence intervals (assuming normal distribution)
            confidence = 0.95
            alpha = 1 - confidence
            from scipy import stats
            
            ci_lower = []
            ci_upper = []
            for _, row in grouped.iterrows():
                if row['count'] > 1:
                    sem = row['std'] / np.sqrt(row['count'])
                    ci = stats.t.interval(confidence, row['count']-1, loc=row['mean'], scale=sem)
                    ci_lower.append(ci[0])
                    ci_upper.append(ci[1])
                else:
                    ci_lower.append(row['mean'])
                    ci_upper.append(row['mean'])
            
            # Plot mean line
            ax.plot(grouped['step'], grouped['mean'], 
                   color=colors[i], linewidth=2.5, label=algorithm.upper())
            
            # Plot confidence interval
            ax.fill_between(grouped['step'], ci_lower, ci_upper,
                           color=colors[i], alpha=0.2)
        
        # Formatting
        ax.set_xlabel('Training Steps', fontsize=12)
        ax.set_ylabel('Average Return', fontsize=12)
        ax.set_title('Actor-Critic Baseline Comparison: Learning Curves\n'
                    f'({len(self.eval_df["seed"].unique())} seeds per algorithm)', 
                    fontsize=14, fontweight='bold')
        ax.legend(fontsize=11)
        ax.grid(True, alpha=0.3)
        
        # Add success threshold
        ax.axhline(y=200, color='red', linestyle='--', alpha=0.7, 
                  label='Success threshold (200)')
        ax.legend(fontsize=11)
        
        plt.tight_layout()
        
        if save_plot:
            plot_path = self.output_dir / "learning_curves.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"Learning curves saved to {plot_path}")
        
        if show_plot:
            plt.show()
        else:
            plt.close()
    
    def prepare_rliable_data(self) -> Dict[str, np.ndarray]:
        """
        Prepare data in RLiable format.
        
        Returns
        -------
        score_dict : Dict[str, np.ndarray]
            Dictionary with algorithm names as keys and score arrays as values
            Shape: {algorithm: np.array([seed_scores])}
        """
        if self.final_df is None:
            print("No final results data available")
            return {}
        
        score_dict = {}
        algorithms = self.final_df['algorithm'].unique()
        
        for algorithm in algorithms:
            alg_data = self.final_df[self.final_df['algorithm'] == algorithm]
            scores = alg_data['final_return'].values
            score_dict[algorithm.upper()] = scores
        
        print(f"Prepared RLiable data for {len(score_dict)} algorithms")
        for alg, scores in score_dict.items():
            print(f"  {alg}: {len(scores)} seeds, mean={np.mean(scores):.1f}")
        
        return score_dict
    
    def plot_rliable_analysis(self, save_plot: bool = True, show_plot: bool = False) -> None:
        """
        Create comprehensive RLiable analysis plots.
        
        Parameters
        ----------
        save_plot : bool
            Whether to save the plots
        show_plot : bool
            Whether to display the plots
        """
        if not RLIABLE_AVAILABLE:
            print("RLiable not available. Cannot create RLiable analysis.")
            return
        
        score_dict = self.prepare_rliable_data()
        if not score_dict:
            return
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('RLiable Statistical Analysis: Actor-Critic Baselines', 
                    fontsize=16, fontweight='bold')
        
        # 1. Aggregate metrics with confidence intervals
        ax1 = axes[0, 0]
        aggregate_func = lambda x: np.array([metrics.aggregate_median(x),
                                           metrics.aggregate_mean(x),
                                           metrics.aggregate_optimality_gap(x)])
        aggregate_scores, aggregate_cis = rly.get_interval_estimates(
            score_dict, aggregate_func, reps=50000)
        
        plot_utils.plot_interval_estimates(
            aggregate_scores, aggregate_cis,
            metric_names=['Median', 'Mean', 'Optimality Gap'],
            algorithms=list(score_dict.keys()),
            xlabel='Algorithms',
            ax=ax1)
        ax1.set_title('Aggregate Performance Metrics')
        
        # 2. Performance profiles
        ax2 = axes[0, 1]
        # Normalize scores for performance profiles
        all_scores = np.concatenate(list(score_dict.values()))
        min_score = np.min(all_scores)
        max_score = np.max(all_scores)
        
        normalized_dict = {}
        for alg, scores in score_dict.items():
            normalized_dict[alg] = (scores - min_score) / (max_score - min_score)
        
        thresholds = np.linspace(0, 1, 21)
        score_distributions, score_distributions_cis = rly.get_interval_estimates(
            normalized_dict, 
            lambda x: np.array([metrics.aggregate_scores(x, threshold) for threshold in thresholds]).T,
            reps=50000)
        
        plot_utils.plot_performance_profiles(
            score_distributions, thresholds,
            performance_profile_cis=score_distributions_cis,
            colors=dict(zip(score_dict.keys(), plt.cm.Set1(np.linspace(0, 1, len(score_dict))))),
            xlabel='Normalized Score Threshold',
            ax=ax2)
        ax2.set_title('Performance Profiles')
        
        # 3. Sample efficiency (if we have learning curve data)
        ax3 = axes[1, 0]
        if self.eval_df is not None:
            self._plot_sample_efficiency(ax3, score_dict)
        else:
            ax3.text(0.5, 0.5, 'No learning curve data\navailable for sample efficiency', 
                    ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('Sample Efficiency (N/A)')
        
        # 4. Probability of improvement
        ax4 = axes[1, 1]
        self._plot_probability_improvement(ax4, score_dict)
        
        plt.tight_layout()
        
        if save_plot:
            plot_path = self.output_dir / "rliable_analysis.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"RLiable analysis saved to {plot_path}")
        
        if show_plot:
            plt.show()
        else:
            plt.close()
    
    def _plot_sample_efficiency(self, ax, score_dict: Dict[str, np.ndarray]) -> None:
        """Plot sample efficiency curves."""
        if self.eval_df is None:
            return
        
        thresholds = np.linspace(0, 200, 11)  # Performance thresholds
        algorithms = list(score_dict.keys())
        
        for algorithm in algorithms:
            alg_data = self.eval_df[self.eval_df['algorithm'] == algorithm.lower()]
            seeds = alg_data['seed'].unique()
            
            steps_to_threshold = []
            for threshold in thresholds:
                seed_steps = []
                for seed in seeds:
                    seed_data = alg_data[alg_data['seed'] == seed].sort_values('step')
                    exceeds = seed_data[seed_data['return'] >= threshold]
                    if len(exceeds) > 0:
                        seed_steps.append(exceeds.iloc[0]['step'])
                    else:
                        seed_steps.append(seed_data.iloc[-1]['step'])  # Never reached
                steps_to_threshold.append(np.mean(seed_steps))
            
            ax.plot(thresholds, steps_to_threshold, marker='o', label=algorithm)
        
        ax.set_xlabel('Performance Threshold')
        ax.set_ylabel('Steps to Reach Threshold')
        ax.set_title('Sample Efficiency')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_probability_improvement(self, ax, score_dict: Dict[str, np.ndarray]) -> None:
        """Plot probability of improvement analysis."""
        algorithms = list(score_dict.keys())
        
        if len(algorithms) < 2:
            ax.text(0.5, 0.5, 'Need at least 2 algorithms\nfor comparison', 
                   ha='center', va='center', transform=ax.transAxes)
            ax.set_title('Probability of Improvement (N/A)')
            return
        
        # Compare each algorithm against the best one
        best_alg = max(score_dict.keys(), key=lambda k: np.mean(score_dict[k]))
        best_scores = score_dict[best_alg]
        
        prob_improvements = {}
        for alg in algorithms:
            if alg != best_alg:
                alg_scores = score_dict[alg]
                # Calculate probability that alg > best_alg
                prob_better = np.mean(alg_scores[:, None] > best_scores[None, :])
                prob_improvements[alg] = prob_better
        
        if prob_improvements:
            algs = list(prob_improvements.keys())
            probs = list(prob_improvements.values())
            
            bars = ax.bar(algs, probs, alpha=0.7)
            ax.set_ylabel('Probability of Improvement')
            ax.set_title(f'Probability of Outperforming {best_alg}')
            ax.set_ylim(0, 1)
            
            # Add value labels on bars
            for bar, prob in zip(bars, probs):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{prob:.3f}', ha='center', va='bottom')
    
    def plot_summary_statistics(self, save_plot: bool = True, show_plot: bool = False) -> None:
        """
        Plot summary statistics and comparisons.
        
        Parameters
        ----------
        save_plot : bool
            Whether to save the plot
        show_plot : bool
            Whether to display the plot
        """
        if self.final_df is None:
            print("No final results data available")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Summary Statistics: Actor-Critic Baselines', 
                    fontsize=16, fontweight='bold')
        
        algorithms = self.final_df['algorithm'].unique()
        
        # 1. Box plot of final returns
        ax1 = axes[0, 0]
        data_for_box = [self.final_df[self.final_df['algorithm'] == alg]['final_return'].values 
                       for alg in algorithms]
        box_plot = ax1.boxplot(data_for_box, labels=[alg.upper() for alg in algorithms], 
                              patch_artist=True)
        
        # Color the boxes
        colors = plt.cm.Set1(np.linspace(0, 1, len(algorithms)))
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        ax1.set_ylabel('Final Return')
        ax1.set_title('Distribution of Final Returns')
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=200, color='red', linestyle='--', alpha=0.7)
        
        # 2. Success rates
        ax2 = axes[0, 1]
        success_rates = []
        for alg in algorithms:
            alg_data = self.final_df[self.final_df['algorithm'] == alg]
            success_rate = np.mean(alg_data['final_return'] > 200)
            success_rates.append(success_rate)
        
        bars = ax2.bar([alg.upper() for alg in algorithms], success_rates, 
                      color=colors, alpha=0.7)
        ax2.set_ylabel('Success Rate')
        ax2.set_title('Success Rate (Return > 200)')
        ax2.set_ylim(0, 1)
        
        # Add value labels
        for bar, rate in zip(bars, success_rates):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{rate:.1%}', ha='center', va='bottom')
        
        # 3. Training time comparison
        ax3 = axes[1, 0]
        training_times = []
        training_stds = []
        for alg in algorithms:
            alg_data = self.final_df[self.final_df['algorithm'] == alg]
            training_times.append(alg_data['training_time'].mean())
            training_stds.append(alg_data['training_time'].std())
        
        ax3.bar([alg.upper() for alg in algorithms], training_times, 
               yerr=training_stds, color=colors, alpha=0.7, capsize=5)
        ax3.set_ylabel('Training Time (seconds)')
        ax3.set_title('Training Time Comparison')
        
        # 4. Performance vs Training Time
        ax4 = axes[1, 1]
        for i, alg in enumerate(algorithms):
            alg_data = self.final_df[self.final_df['algorithm'] == alg]
            ax4.scatter(alg_data['training_time'], alg_data['final_return'], 
                       color=colors[i], label=alg.upper(), alpha=0.7, s=60)
        
        ax4.set_xlabel('Training Time (seconds)')
        ax4.set_ylabel('Final Return')
        ax4.set_title('Performance vs Training Time')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plot:
            plot_path = self.output_dir / "summary_statistics.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            print(f"Summary statistics saved to {plot_path}")
        
        if show_plot:
            plt.show()
        else:
            plt.close()
    
    def create_all_plots(self, save_plots: bool = True, show_plots: bool = False) -> None:
        """
        Create all available plots.
        
        Parameters
        ----------
        save_plots : bool
            Whether to save the plots
        show_plots : bool
            Whether to display the plots
        """
        print("Creating all visualization plots...")
        
        # Learning curves
        self.plot_learning_curves(save_plot=save_plots, show_plot=show_plots)
        
        # Summary statistics
        self.plot_summary_statistics(save_plot=save_plots, show_plot=show_plots)
        
        # RLiable analysis (if available)
        if RLIABLE_AVAILABLE:
            self.plot_rliable_analysis(save_plot=save_plots, show_plot=show_plots)
        else:
            print("Skipping RLiable analysis (not available)")
        
        print("All plots created!")
    
    def print_summary(self) -> None:
        """Print a summary of the results."""
        if self.final_df is None:
            print("No final results data available")
            return
        
        print("\n" + "="*60)
        print("RESULTS SUMMARY")
        print("="*60)
        
        algorithms = self.final_df['algorithm'].unique()
        
        # Calculate statistics
        stats = []
        for alg in algorithms:
            alg_data = self.final_df[self.final_df['algorithm'] == alg]
            stats.append({
                'algorithm': alg.upper(),
                'mean': alg_data['final_return'].mean(),
                'std': alg_data['final_return'].std(),
                'min': alg_data['final_return'].min(),
                'max': alg_data['final_return'].max(),
                'success_rate': np.mean(alg_data['final_return'] > 200),
                'training_time': alg_data['training_time'].mean(),
                'seeds': len(alg_data)
            })
        
        # Sort by mean performance
        stats.sort(key=lambda x: x['mean'], reverse=True)
        
        print("PERFORMANCE RANKING:")
        print("-" * 40)
        for rank, stat in enumerate(stats, 1):
            print(f"{rank}. {stat['algorithm']:8s}: {stat['mean']:6.1f} ± {stat['std']:4.1f} "
                  f"(success: {stat['success_rate']:.1%}, {stat['seeds']} seeds)")
        
        print("\nDETAILED STATISTICS:")
        print("-" * 40)
        for stat in stats:
            print(f"\n{stat['algorithm']} Baseline:")
            print(f"  Mean Return: {stat['mean']:.2f} ± {stat['std']:.2f}")
            print(f"  Range: [{stat['min']:.1f}, {stat['max']:.1f}]")
            print(f"  Success Rate: {stat['success_rate']:.1%}")
            print(f"  Training Time: {stat['training_time']:.1f}s")
        
        print("="*60)


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description="Visualize Actor-Critic baseline comparison results")
    
    parser.add_argument("results_dir", type=str,
                       help="Directory containing CSV result files")
    parser.add_argument("--show-plots", action="store_true",
                       help="Display plots interactively")
    parser.add_argument("--no-save", action="store_true",
                       help="Don't save plots to files")
    
    args = parser.parse_args()
    
    # Create visualizer
    visualizer = ResultsVisualizer(args.results_dir)
    
    # Print summary
    visualizer.print_summary()
    
    # Create plots
    visualizer.create_all_plots(
        save_plots=not args.no_save,
        show_plots=args.show_plots
    )
    
    print(f"\n✅ Visualization completed! Plots saved to: {visualizer.output_dir}")


if __name__ == "__main__":
    main()
